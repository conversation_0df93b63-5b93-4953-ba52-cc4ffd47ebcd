{"version": 3, "sources": ["../../src/build/is-writeable.ts"], "sourcesContent": ["import fs from 'fs'\n\nexport async function isWriteable(directory: string): Promise<boolean> {\n  try {\n    await fs.promises.access(directory, (fs.constants || fs).W_OK)\n    return true\n  } catch (err) {\n    return false\n  }\n}\n"], "names": ["fs", "isWriteable", "directory", "promises", "access", "constants", "W_OK", "err"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAEnB,OAAO,eAAeC,YAAYC,SAAiB;IACjD,IAAI;QACF,MAAMF,GAAGG,QAAQ,CAACC,MAAM,CAACF,WAAW,AAACF,CAAAA,GAAGK,SAAS,IAAIL,EAAC,EAAGM,IAAI;QAC7D,OAAO;IACT,EAAE,OAAOC,KAAK;QACZ,OAAO;IACT;AACF"}