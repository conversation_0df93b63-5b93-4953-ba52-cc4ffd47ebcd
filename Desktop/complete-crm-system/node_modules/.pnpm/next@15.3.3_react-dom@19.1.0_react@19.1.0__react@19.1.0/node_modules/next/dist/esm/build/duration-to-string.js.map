{"version": 3, "sources": ["../../src/build/duration-to-string.ts"], "sourcesContent": ["export function durationToString(compilerDuration: number) {\n  let durationString\n  if (compilerDuration > 120) {\n    durationString = `${(compilerDuration / 60).toFixed(1)}min`\n  } else if (compilerDuration > 40) {\n    durationString = `${compilerDuration.toFixed(0)}s`\n  } else if (compilerDuration > 2) {\n    durationString = `${compilerDuration.toFixed(1)}s`\n  } else {\n    durationString = `${(compilerDuration * 1000).toFixed(0)}ms`\n  }\n  return durationString\n}\n"], "names": ["durationToString", "compilerDuration", "durationString", "toFixed"], "mappings": "AAAA,OAAO,SAASA,iBAAiBC,gBAAwB;IACvD,IAAIC;IACJ,IAAID,mBAAmB,KAAK;QAC1BC,iBAAiB,GAAG,AAACD,CAAAA,mBAAmB,EAAC,EAAGE,OAAO,CAAC,GAAG,GAAG,CAAC;IAC7D,OAAO,IAAIF,mBAAmB,IAAI;QAChCC,iBAAiB,GAAGD,iBAAiBE,OAAO,CAAC,GAAG,CAAC,CAAC;IACpD,OAAO,IAAIF,mBAAmB,GAAG;QAC/BC,iBAAiB,GAAGD,iBAAiBE,OAAO,CAAC,GAAG,CAAC,CAAC;IACpD,OAAO;QACLD,iBAAiB,GAAG,AAACD,CAAAA,mBAAmB,IAAG,EAAGE,OAAO,CAAC,GAAG,EAAE,CAAC;IAC9D;IACA,OAAOD;AACT"}