{"version": 3, "sources": ["../../../src/client/legacy/image.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  type JSX,\n} from 'react'\nimport * as ReactDOM from 'react-dom'\nimport Head from '../../shared/lib/head'\nimport {\n  imageConfigDefault,\n  VALID_LOADERS,\n} from '../../shared/lib/image-config'\nimport type {\n  ImageConfigComplete,\n  LoaderValue,\n} from '../../shared/lib/image-config'\nimport { useIntersection } from '../use-intersection'\nimport { ImageConfigContext } from '../../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport { normalizePathTrailingSlash } from '../normalize-trailing-slash'\n\nfunction normalizeSrc(src: string): string {\n  return src[0] === '/' ? src.slice(1) : src\n}\n\nconst supportsFloat = typeof ReactDOM.preload === 'function'\nconst DEFAULT_Q = 75\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\nconst loadedImageURLs = new Set<string>()\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: string }\n>()\nlet perfObserver: PerformanceObserver | undefined\nconst emptyDataURL =\n  'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & { allSizes: number[] }\nexport type ImageLoader = (resolverProps: ImageLoaderProps) => string\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (\n  resolverProps: ImageLoaderPropsWithConfig\n) => string\ntype ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nfunction imgixLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  // Demo: https://static.imgix.net/daisy.png?auto=format&fit=max&w=300\n  const url = new URL(`${config.path}${normalizeSrc(src)}`)\n  const params = url.searchParams\n\n  // auto params can be combined with comma separation, or reiteration\n  params.set('auto', params.getAll('auto').join(',') || 'format')\n  params.set('fit', params.get('fit') || 'max')\n  params.set('w', params.get('w') || width.toString())\n\n  if (quality) {\n    params.set('q', quality.toString())\n  }\n\n  return url.href\n}\n\nfunction akamaiLoader({\n  config,\n  src,\n  width,\n}: ImageLoaderPropsWithConfig): string {\n  return `${config.path}${normalizeSrc(src)}?imwidth=${width}`\n}\n\nfunction cloudinaryLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  // Demo: https://res.cloudinary.com/demo/image/upload/w_300,c_limit,q_auto/turtles.jpg\n  const params = ['f_auto', 'c_limit', 'w_' + width, 'q_' + (quality || 'auto')]\n  const paramsString = params.join(',') + '/'\n  return `${config.path}${paramsString}${normalizeSrc(src)}`\n}\n\nfunction customLoader({ src }: ImageLoaderProps): string {\n  throw new Error(\n    `Image with src \"${src}\" is missing \"loader\" prop.` +\n      `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n  )\n}\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const {\n          hasLocalMatch,\n        } = require('../../shared/lib/match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const {\n          hasRemoteMatch,\n        } = require('../../shared/lib/match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  if (!config.dangerouslyAllowSVG && src.split('?', 1)[0].endsWith('.svg')) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    return src\n  }\n\n  return `${normalizePathTrailingSlash(config.path)}?url=${encodeURIComponent(\n    src\n  )}&w=${width}&q=${q}`\n}\n\nconst loaders = new Map<\n  LoaderValue,\n  (props: ImageLoaderPropsWithConfig) => string\n>([\n  ['default', defaultLoader],\n  ['imgix', imgixLoader],\n  ['cloudinary', cloudinaryLoader],\n  ['akamai', akamaiLoader],\n  ['custom', customLoader],\n])\n\nconst VALID_LAYOUT_VALUES = [\n  'fill',\n  'fixed',\n  'intrinsic',\n  'responsive',\n  undefined,\n] as const\ntype LayoutValue = (typeof VALID_LAYOUT_VALUES)[number]\n\ntype PlaceholderValue = 'blur' | 'empty'\n\ntype OnLoadingComplete = (result: {\n  naturalWidth: number\n  naturalHeight: number\n}) => void\n\ntype ImgElementStyle = NonNullable<JSX.IntrinsicElements['img']['style']>\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n}\n\ninterface StaticRequire {\n  default: StaticImageData\n}\n\ntype StaticImport = StaticRequire | StaticImageData\n\ntype SafeNumber = number | `${number}`\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  width?: SafeNumber\n  height?: SafeNumber\n  layout?: LayoutValue\n  loader?: ImageLoader\n  quality?: SafeNumber\n  priority?: boolean\n  loading?: LoadingValue\n  lazyRoot?: React.RefObject<HTMLElement | null> | null\n  lazyBoundary?: string\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  objectFit?: ImgElementStyle['objectFit']\n  objectPosition?: ImgElementStyle['objectPosition']\n  onLoadingComplete?: OnLoadingComplete\n}\n\ntype ImageElementProps = Omit<ImageProps, 'src' | 'loader'> & {\n  srcString: string\n  imgAttributes: GenImgAttrsResult\n  heightInt: number | undefined\n  widthInt: number | undefined\n  qualityInt: number | undefined\n  layout: LayoutValue\n  imgStyle: ImgElementStyle\n  blurStyle: ImgElementStyle\n  isLazy: boolean\n  loading: LoadingValue\n  config: ImageConfig\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  placeholder: PlaceholderValue\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setIntersection: (img: HTMLImageElement | null) => void\n  isVisible: boolean\n  noscriptSizes: string | undefined\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  layout: LayoutValue,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes && (layout === 'fill' || layout === 'responsive')) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (\n    typeof width !== 'number' ||\n    layout === 'fill' ||\n    layout === 'responsive'\n  ) {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  layout: LayoutValue\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  layout,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, layout, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'number') {\n    return x\n  }\n  if (typeof x === 'string') {\n    return parseInt(x, 10)\n  }\n  return undefined\n}\n\nfunction defaultImageLoader(loaderProps: ImageLoaderPropsWithConfig) {\n  const loaderKey = loaderProps.config?.loader || 'default'\n  const load = loaders.get(loaderKey)\n  if (load) {\n    return load(loaderProps)\n  }\n  throw new Error(\n    `Unknown \"loader\" found in \"next.config.js\". Expected: ${VALID_LOADERS.join(\n      ', '\n    )}. Received: ${loaderKey}`\n  )\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  src: string,\n  layout: LayoutValue,\n  placeholder: PlaceholderValue,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void\n) {\n  if (!img || img.src === emptyDataURL || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentNode) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    loadedImageURLs.add(src)\n    if (placeholder === 'blur') {\n      setBlurComplete(true)\n    }\n    if (onLoadingCompleteRef?.current) {\n      const { naturalWidth, naturalHeight } = img\n      // Pass back read-only primitive values but not the\n      // underlying DOM element because it could be misused.\n      onLoadingCompleteRef.current({ naturalWidth, naturalHeight })\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (img.parentElement?.parentElement) {\n        const parent = getComputedStyle(img.parentElement.parentElement)\n        if (!parent.position) {\n          // The parent has not been rendered to the dom yet and therefore it has no position. Skip the warnings for such cases.\n        } else if (layout === 'responsive' && parent.display === 'flex') {\n          warnOnce(\n            `Image with src \"${src}\" may not render properly as a child of a flex container. Consider wrapping the image with a div to configure the width.`\n          )\n        } else if (\n          layout === 'fill' &&\n          parent.position !== 'relative' &&\n          parent.position !== 'fixed' &&\n          parent.position !== 'absolute'\n        ) {\n          warnOnce(\n            `Image with src \"${src}\" may not render properly with a parent using position:\"${parent.position}\". Consider changing the parent style to position:\"relative\" with a width and height.`\n          )\n        }\n      }\n    }\n  })\n}\n\nconst ImageElement = ({\n  imgAttributes,\n  heightInt,\n  widthInt,\n  qualityInt,\n  layout,\n  className,\n  imgStyle,\n  blurStyle,\n  isLazy,\n  placeholder,\n  loading,\n  srcString,\n  config,\n  unoptimized,\n  loader,\n  onLoadingCompleteRef,\n  setBlurComplete,\n  setIntersection,\n  onLoad,\n  onError,\n  isVisible,\n  noscriptSizes,\n  ...rest\n}: ImageElementProps) => {\n  loading = isLazy ? 'lazy' : loading\n  return (\n    <>\n      <img\n        {...rest}\n        {...imgAttributes}\n        decoding=\"async\"\n        data-nimg={layout}\n        className={className}\n        style={{ ...imgStyle, ...blurStyle }}\n        ref={useCallback(\n          (img: ImgElementWithDataProp) => {\n            if (process.env.NODE_ENV !== 'production') {\n              if (img && !srcString) {\n                console.error(`Image is missing required \"src\" property:`, img)\n              }\n            }\n            setIntersection(img)\n            if (img?.complete) {\n              handleLoading(\n                img,\n                srcString,\n                layout,\n                placeholder,\n                onLoadingCompleteRef,\n                setBlurComplete\n              )\n            }\n          },\n          [\n            setIntersection,\n            srcString,\n            layout,\n            placeholder,\n            onLoadingCompleteRef,\n            setBlurComplete,\n          ]\n        )}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            srcString,\n            layout,\n            placeholder,\n            onLoadingCompleteRef,\n            setBlurComplete\n          )\n          if (onLoad) {\n            onLoad(event)\n          }\n        }}\n        onError={(event) => {\n          if (placeholder === 'blur') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n      {(isLazy || placeholder === 'blur') && (\n        <noscript>\n          <img\n            {...rest}\n            // @ts-ignore - TODO: upgrade to `@types/react@17`\n            loading={loading}\n            decoding=\"async\"\n            data-nimg={layout}\n            style={imgStyle}\n            className={className}\n            // It's intended to keep `loading` before `src` because React updates\n            // props in order which causes Safari/Firefox to not lazy load properly.\n            // See https://github.com/facebook/react/issues/25883\n            {...generateImgAttrs({\n              config,\n              src: srcString,\n              unoptimized,\n              layout,\n              width: widthInt,\n              quality: qualityInt,\n              sizes: noscriptSizes,\n              loader,\n            })}\n          />\n        </noscript>\n      )}\n    </>\n  )\n}\n\nexport default function Image({\n  src,\n  sizes,\n  unoptimized = false,\n  priority = false,\n  loading,\n  lazyRoot = null,\n  lazyBoundary,\n  className,\n  quality,\n  width,\n  height,\n  style,\n  objectFit,\n  objectPosition,\n  onLoadingComplete,\n  placeholder = 'empty',\n  blurDataURL,\n  ...all\n}: ImageProps) {\n  const configContext = useContext(ImageConfigContext)\n  const config: ImageConfig = useMemo(() => {\n    const c = configEnv || configContext || imageConfigDefault\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    return { ...c, allSizes, deviceSizes, qualities }\n  }, [configContext])\n\n  let rest: Partial<ImageProps> = all\n  let layout: NonNullable<LayoutValue> = sizes ? 'responsive' : 'intrinsic'\n  if ('layout' in rest) {\n    // Override default layout if the user specified one:\n    if (rest.layout) layout = rest.layout\n\n    // Remove property so it's not spread on <img>:\n    delete rest.layout\n  }\n\n  let loader: ImageLoaderWithConfig = defaultImageLoader\n  if ('loader' in rest) {\n    if (rest.loader) {\n      const customImageLoader = rest.loader\n      loader = (obj) => {\n        const { config: _, ...opts } = obj\n        // The config object is internal only so we must\n        // not pass it to the user-defined loader()\n        return customImageLoader(opts)\n      }\n    }\n    // Remove property so it's not spread on <img>\n    delete rest.loader\n  }\n\n  let staticSrc = ''\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n    if (!layout || layout !== 'fill') {\n      height = height || staticImageData.height\n      width = width || staticImageData.width\n      if (!staticImageData.height || !staticImageData.width) {\n        throw new Error(\n          `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n            staticImageData\n          )}`\n        )\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (typeof window !== 'undefined' && loadedImageURLs.has(src)) {\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n\n  const [blurComplete, setBlurComplete] = useState(false)\n  const [setIntersection, isIntersected, resetIntersected] =\n    useIntersection<HTMLImageElement>({\n      rootRef: lazyRoot,\n      rootMargin: lazyBoundary || '200px',\n      disabled: !isLazy,\n    })\n  const isVisible = !isLazy || isIntersected\n\n  const wrapperStyle: JSX.IntrinsicElements['span']['style'] = {\n    boxSizing: 'border-box',\n    display: 'block',\n    overflow: 'hidden',\n    width: 'initial',\n    height: 'initial',\n    background: 'none',\n    opacity: 1,\n    border: 0,\n    margin: 0,\n    padding: 0,\n  }\n  const sizerStyle: JSX.IntrinsicElements['span']['style'] = {\n    boxSizing: 'border-box',\n    display: 'block',\n    width: 'initial',\n    height: 'initial',\n    background: 'none',\n    opacity: 1,\n    border: 0,\n    margin: 0,\n    padding: 0,\n  }\n  let hasSizer = false\n  let sizerSvgUrl: string | undefined\n  const layoutStyle: ImgElementStyle = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n\n    boxSizing: 'border-box',\n    padding: 0,\n    border: 'none',\n    margin: 'auto',\n\n    display: 'block',\n    width: 0,\n    height: 0,\n    minWidth: '100%',\n    maxWidth: '100%',\n    minHeight: '100%',\n    maxHeight: '100%',\n\n    objectFit,\n    objectPosition,\n  }\n\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      widthInt = widthInt || 1\n      heightInt = heightInt || 1\n      unoptimized = true\n    } else {\n      if (!VALID_LAYOUT_VALUES.includes(layout)) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"layout\" property. Provided \"${layout}\" should be one of ${VALID_LAYOUT_VALUES.map(\n            String\n          ).join(',')}.`\n        )\n      }\n\n      if (\n        (typeof widthInt !== 'undefined' && isNaN(widthInt)) ||\n        (typeof heightInt !== 'undefined' && isNaN(heightInt))\n      ) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"width\" or \"height\" property. These should be numeric values.`\n        )\n      }\n      if (layout === 'fill' && (width || height)) {\n        warnOnce(\n          `Image with src \"${src}\" and \"layout='fill'\" has unused properties assigned. Please remove \"width\" and \"height\".`\n        )\n      }\n      if (!VALID_LOADING_VALUES.includes(loading)) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n            String\n          ).join(',')}.`\n        )\n      }\n      if (priority && loading === 'lazy') {\n        throw new Error(\n          `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n        )\n      }\n      if (sizes && layout !== 'fill' && layout !== 'responsive') {\n        warnOnce(\n          `Image with src \"${src}\" has \"sizes\" property but it will be ignored. Only use \"sizes\" with \"layout='fill'\" or \"layout='responsive'\"`\n        )\n      }\n      if (placeholder === 'blur') {\n        if (layout !== 'fill' && (widthInt || 0) * (heightInt || 0) < 1600) {\n          warnOnce(\n            `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder='blur'\" property to improve performance.`\n          )\n        }\n        if (!blurDataURL) {\n          const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n          throw new Error(\n            `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n          Possible solutions:\n            - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n            - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n              ','\n            )} (animated images not supported)\n            - Remove the \"placeholder\" property, effectively no blur effect\n          Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n          )\n        }\n      }\n      if ('ref' in rest) {\n        warnOnce(\n          `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoadingComplete\" property instead.`\n        )\n      }\n\n      if (!unoptimized && loader !== defaultImageLoader) {\n        const urlStr = loader({\n          config,\n          src,\n          width: widthInt || 400,\n          quality: qualityInt || 75,\n        })\n        let url: URL | undefined\n        try {\n          url = new URL(urlStr)\n        } catch (err) {}\n        if (urlStr === src || (url && url.pathname === src && !url.search)) {\n          warnOnce(\n            `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n              `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n          )\n        }\n      }\n\n      if (style) {\n        let overwrittenStyles = Object.keys(style).filter(\n          (key) => key in layoutStyle\n        )\n        if (overwrittenStyles.length) {\n          warnOnce(\n            `Image with src ${src} is assigned the following styles, which are overwritten by automatically-generated styles: ${overwrittenStyles.join(\n              ', '\n            )}`\n          )\n        }\n      }\n\n      if (\n        typeof window !== 'undefined' &&\n        !perfObserver &&\n        window.PerformanceObserver\n      ) {\n        perfObserver = new PerformanceObserver((entryList) => {\n          for (const entry of entryList.getEntries()) {\n            // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n            const imgSrc = entry?.element?.src || ''\n            const lcpImage = allImgs.get(imgSrc)\n            if (\n              lcpImage &&\n              !lcpImage.priority &&\n              lcpImage.placeholder !== 'blur' &&\n              !lcpImage.src.startsWith('data:') &&\n              !lcpImage.src.startsWith('blob:')\n            ) {\n              // https://web.dev/lcp/#measure-lcp-in-javascript\n              warnOnce(\n                `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                  `\\nRead more: https://nextjs.org/docs/api-reference/next/legacy/image#priority`\n              )\n            }\n          }\n        })\n        try {\n          perfObserver.observe({\n            type: 'largest-contentful-paint',\n            buffered: true,\n          })\n        } catch (err) {\n          // Log error but don't crash the app\n          console.error(err)\n        }\n      }\n    }\n  }\n  const imgStyle = Object.assign({}, style, layoutStyle)\n  const blurStyle =\n    placeholder === 'blur' && !blurComplete\n      ? {\n          backgroundSize: objectFit || 'cover',\n          backgroundPosition: objectPosition || '0% 0%',\n          filter: 'blur(20px)',\n          backgroundImage: `url(\"${blurDataURL}\")`,\n        }\n      : {}\n  if (layout === 'fill') {\n    // <Image src=\"i.png\" layout=\"fill\" />\n    wrapperStyle.display = 'block'\n    wrapperStyle.position = 'absolute'\n    wrapperStyle.top = 0\n    wrapperStyle.left = 0\n    wrapperStyle.bottom = 0\n    wrapperStyle.right = 0\n  } else if (\n    typeof widthInt !== 'undefined' &&\n    typeof heightInt !== 'undefined'\n  ) {\n    // <Image src=\"i.png\" width=\"100\" height=\"100\" />\n    const quotient = heightInt / widthInt\n    const paddingTop = isNaN(quotient) ? '100%' : `${quotient * 100}%`\n    if (layout === 'responsive') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"responsive\" />\n      wrapperStyle.display = 'block'\n      wrapperStyle.position = 'relative'\n      hasSizer = true\n      sizerStyle.paddingTop = paddingTop\n    } else if (layout === 'intrinsic') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"intrinsic\" />\n      wrapperStyle.display = 'inline-block'\n      wrapperStyle.position = 'relative'\n      wrapperStyle.maxWidth = '100%'\n      hasSizer = true\n      sizerStyle.maxWidth = '100%'\n      sizerSvgUrl = `data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27${widthInt}%27%20height=%27${heightInt}%27/%3e`\n    } else if (layout === 'fixed') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"fixed\" />\n      wrapperStyle.display = 'inline-block'\n      wrapperStyle.position = 'relative'\n      wrapperStyle.width = widthInt\n      wrapperStyle.height = heightInt\n    }\n  } else {\n    // <Image src=\"i.png\" />\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        `Image with src \"${src}\" must use \"width\" and \"height\" properties or \"layout='fill'\" property.`\n      )\n    }\n  }\n\n  let imgAttributes: GenImgAttrsResult = {\n    src: emptyDataURL,\n    srcSet: undefined,\n    sizes: undefined,\n  }\n\n  if (isVisible) {\n    imgAttributes = generateImgAttrs({\n      config,\n      src,\n      unoptimized,\n      layout,\n      width: widthInt,\n      quality: qualityInt,\n      sizes,\n      loader,\n    })\n  }\n\n  let srcString: string = src\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const linkProps:\n    | React.DetailedHTMLProps<\n        React.LinkHTMLAttributes<HTMLLinkElement>,\n        HTMLLinkElement\n      >\n    | undefined = supportsFloat\n    ? undefined\n    : {\n        imageSrcSet: imgAttributes.srcSet,\n        imageSizes: imgAttributes.sizes,\n        crossOrigin: rest.crossOrigin,\n        referrerPolicy: rest.referrerPolicy,\n      }\n\n  const useLayoutEffect =\n    typeof window === 'undefined' ? React.useEffect : React.useLayoutEffect\n  const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n  const previousImageSrc = useRef<string | StaticImport>(src)\n  useEffect(() => {\n    onLoadingCompleteRef.current = onLoadingComplete\n  }, [onLoadingComplete])\n\n  useLayoutEffect(() => {\n    if (previousImageSrc.current !== src) {\n      resetIntersected()\n      previousImageSrc.current = src\n    }\n  }, [resetIntersected, src])\n\n  const imgElementArgs = {\n    isLazy,\n    imgAttributes,\n    heightInt,\n    widthInt,\n    qualityInt,\n    layout,\n    className,\n    imgStyle,\n    blurStyle,\n    loading,\n    config,\n    unoptimized,\n    placeholder,\n    loader,\n    srcString,\n    onLoadingCompleteRef,\n    setBlurComplete,\n    setIntersection,\n    isVisible,\n    noscriptSizes: sizes,\n    ...rest,\n  }\n  return (\n    <>\n      <span style={wrapperStyle}>\n        {hasSizer ? (\n          <span style={sizerStyle}>\n            {sizerSvgUrl ? (\n              <img\n                style={{\n                  display: 'block',\n                  maxWidth: '100%',\n                  width: 'initial',\n                  height: 'initial',\n                  background: 'none',\n                  opacity: 1,\n                  border: 0,\n                  margin: 0,\n                  padding: 0,\n                }}\n                alt=\"\"\n                aria-hidden={true}\n                src={sizerSvgUrl}\n              />\n            ) : null}\n          </span>\n        ) : null}\n        <ImageElement {...imgElementArgs} />\n      </span>\n      {!supportsFloat && priority ? (\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would likely cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        <Head>\n          <link\n            key={\n              '__nimg-' +\n              imgAttributes.src +\n              imgAttributes.srcSet +\n              imgAttributes.sizes\n            }\n            rel=\"preload\"\n            as=\"image\"\n            href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n            {...linkProps}\n          />\n        </Head>\n      ) : null}\n    </>\n  )\n}\n"], "names": ["Image", "normalizeSrc", "src", "slice", "supportsFloat", "ReactDOM", "preload", "DEFAULT_Q", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "loadedImageURLs", "Set", "allImgs", "Map", "perfObserver", "emptyDataURL", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "VALID_LOADING_VALUES", "undefined", "imgixLoader", "config", "width", "quality", "url", "URL", "path", "params", "searchParams", "set", "getAll", "join", "get", "toString", "href", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsString", "customLoader", "Error", "defaultLoader", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "push", "length", "JSON", "stringify", "startsWith", "localPatterns", "NEXT_RUNTIME", "hasLocalMatch", "require", "domains", "remotePatterns", "parsedSrc", "err", "console", "error", "hasRemoteMatch", "hostname", "qualities", "includes", "q", "reduce", "prev", "cur", "Math", "abs", "dangerouslyAllowSVG", "split", "endsWith", "normalizePathTrailingSlash", "encodeURIComponent", "loaders", "VALID_LAYOUT_VALUES", "isStaticRequire", "default", "isStaticImageData", "isStaticImport", "getWidths", "layout", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "parseInt", "smallestRatio", "min", "widths", "filter", "s", "kind", "map", "w", "find", "p", "generateImgAttrs", "unoptimized", "loader", "srcSet", "last", "i", "getInt", "x", "defaultImageLoader", "loaderProps", "loader<PERSON>ey", "load", "VALID_LOADERS", "handleLoading", "img", "placeholder", "onLoadingCompleteRef", "setBlurComplete", "decode", "Promise", "resolve", "catch", "then", "parentNode", "add", "current", "naturalWidth", "naturalHeight", "parentElement", "parent", "getComputedStyle", "position", "display", "warnOnce", "ImageElement", "imgAttributes", "heightInt", "widthInt", "qualityInt", "className", "imgStyle", "blurStyle", "isLazy", "loading", "srcString", "setIntersection", "onLoad", "onError", "isVisible", "noscriptSizes", "rest", "decoding", "data-nimg", "style", "ref", "useCallback", "complete", "event", "currentTarget", "noscript", "priority", "lazyRoot", "lazyBoundary", "height", "objectFit", "objectPosition", "onLoadingComplete", "blurDataURL", "all", "configContext", "useContext", "ImageConfigContext", "useMemo", "c", "imageConfigDefault", "imageSizes", "sort", "a", "b", "customImageLoader", "obj", "_", "opts", "staticSrc", "staticImageData", "has", "blurComplete", "useState", "isIntersected", "resetIntersected", "useIntersection", "rootRef", "rootMargin", "disabled", "wrapperStyle", "boxSizing", "overflow", "background", "opacity", "border", "margin", "padding", "sizerStyle", "hasSizer", "sizerSvgUrl", "layoutStyle", "top", "left", "bottom", "right", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "String", "isNaN", "VALID_BLUR_EXT", "urlStr", "pathname", "search", "overwrittenStyles", "Object", "keys", "key", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "observe", "type", "buffered", "assign", "backgroundSize", "backgroundPosition", "backgroundImage", "quotient", "paddingTop", "fullUrl", "e", "location", "linkProps", "imageSrcSet", "crossOrigin", "referrerPolicy", "useLayoutEffect", "React", "useEffect", "useRef", "previousImageSrc", "imgElementArgs", "span", "alt", "aria-hidden", "Head", "link", "rel", "as"], "mappings": "AAAA;;;;;+BAwnBA;;;eAAwBA;;;;;;iEA9mBjB;oEACmB;+DACT;6BAIV;iCAKyB;iDACG;0BACV;wCACkB;AAE3C,SAASC,aAAaC,GAAW;IAC/B,OAAOA,GAAG,CAAC,EAAE,KAAK,MAAMA,IAAIC,KAAK,CAAC,KAAKD;AACzC;AAEA,MAAME,gBAAgB,OAAOC,UAASC,OAAO,KAAK;AAClD,MAAMC,YAAY;AAClB,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAC/C,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAIpB,IAAIC;AACJ,MAAMC,eACJ;AAEF,IAAI,OAAOC,WAAW,aAAa;;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAEA,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAqBzD,SAASC,YAAY,KAKQ;IALR,IAAA,EACnBC,MAAM,EACNtB,GAAG,EACHuB,KAAK,EACLC,OAAO,EACoB,GALR;IAMnB,qEAAqE;IACrE,MAAMC,MAAM,IAAIC,IAAI,AAAC,KAAEJ,OAAOK,IAAI,GAAG5B,aAAaC;IAClD,MAAM4B,SAASH,IAAII,YAAY;IAE/B,oEAAoE;IACpED,OAAOE,GAAG,CAAC,QAAQF,OAAOG,MAAM,CAAC,QAAQC,IAAI,CAAC,QAAQ;IACtDJ,OAAOE,GAAG,CAAC,OAAOF,OAAOK,GAAG,CAAC,UAAU;IACvCL,OAAOE,GAAG,CAAC,KAAKF,OAAOK,GAAG,CAAC,QAAQV,MAAMW,QAAQ;IAEjD,IAAIV,SAAS;QACXI,OAAOE,GAAG,CAAC,KAAKN,QAAQU,QAAQ;IAClC;IAEA,OAAOT,IAAIU,IAAI;AACjB;AAEA,SAASC,aAAa,KAIO;IAJP,IAAA,EACpBd,MAAM,EACNtB,GAAG,EACHuB,KAAK,EACsB,GAJP;IAKpB,OAAO,AAAC,KAAED,OAAOK,IAAI,GAAG5B,aAAaC,OAAK,cAAWuB;AACvD;AAEA,SAASc,iBAAiB,KAKG;IALH,IAAA,EACxBf,MAAM,EACNtB,GAAG,EACHuB,KAAK,EACLC,OAAO,EACoB,GALH;IAMxB,sFAAsF;IACtF,MAAMI,SAAS;QAAC;QAAU;QAAW,OAAOL;QAAO,OAAQC,CAAAA,WAAW,MAAK;KAAG;IAC9E,MAAMc,eAAeV,OAAOI,IAAI,CAAC,OAAO;IACxC,OAAO,AAAC,KAAEV,OAAOK,IAAI,GAAGW,eAAevC,aAAaC;AACtD;AAEA,SAASuC,aAAa,KAAyB;IAAzB,IAAA,EAAEvC,GAAG,EAAoB,GAAzB;IACpB,MAAM,qBAGL,CAHK,IAAIwC,MACR,AAAC,qBAAkBxC,MAAI,gCACpB,4EAFC,qBAAA;eAAA;oBAAA;sBAAA;IAGN;AACF;AAEA,SAASyC,cAAc,KAKM;IALN,IAAA,EACrBnB,MAAM,EACNtB,GAAG,EACHuB,KAAK,EACLC,OAAO,EACoB,GALN;QAuFnBF;IAjFF,IAAIf,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,MAAMC,gBAAgB,EAAE;QAExB,yDAAyD;QACzD,IAAI,CAAC3C,KAAK2C,cAAcC,IAAI,CAAC;QAC7B,IAAI,CAACrB,OAAOoB,cAAcC,IAAI,CAAC;QAE/B,IAAID,cAAcE,MAAM,GAAG,GAAG;YAC5B,MAAM,qBAML,CANK,IAAIL,MACR,AAAC,sCAAmCG,cAAcX,IAAI,CACpD,QACA,gGAA+Fc,KAAKC,SAAS,CAC7G;gBAAE/C;gBAAKuB;gBAAOC;YAAQ,KAJpB,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;QAEA,IAAIxB,IAAIgD,UAAU,CAAC,OAAO;YACxB,MAAM,qBAEL,CAFK,IAAIR,MACR,AAAC,0BAAuBxC,MAAI,2GADxB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,IAAIgD,UAAU,CAAC,QAAQ1B,OAAO2B,aAAa,EAAE;YAC/C,IACE1C,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJC,aAAa,EACd,GAAGC,QAAQ;gBACZ,IAAI,CAACD,cAAc7B,OAAO2B,aAAa,EAAEjD,MAAM;oBAC7C,MAAM,qBAGL,CAHK,IAAIwC,MACR,AAAC,uBAAoBxC,MAAI,kGACtB,0FAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAIgD,UAAU,CAAC,QAAS1B,CAAAA,OAAO+B,OAAO,IAAI/B,OAAOgC,cAAc,AAAD,GAAI;YACrE,IAAIC;YACJ,IAAI;gBACFA,YAAY,IAAI7B,IAAI1B;YACtB,EAAE,OAAOwD,KAAK;gBACZC,QAAQC,KAAK,CAACF;gBACd,MAAM,qBAEL,CAFK,IAAIhB,MACR,AAAC,0BAAuBxC,MAAI,kIADxB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IACEO,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJS,cAAc,EACf,GAAGP,QAAQ;gBACZ,IAAI,CAACO,eAAerC,OAAO+B,OAAO,EAAE/B,OAAOgC,cAAc,EAAEC,YAAY;oBACrE,MAAM,qBAGL,CAHK,IAAIf,MACR,AAAC,uBAAoBxC,MAAI,kCAAiCuD,UAAUK,QAAQ,GAAC,gEAC1E,iFAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAIpC,WAAWF,OAAOuC,SAAS,IAAI,CAACvC,OAAOuC,SAAS,CAACC,QAAQ,CAACtC,UAAU;YACtE,MAAM,qBAGL,CAHK,IAAIgB,MACR,AAAC,2BAAwBhB,UAAQ,8FAC9B,sFAFC,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;IAEA,MAAMuC,IACJvC,aACAF,oBAAAA,OAAOuC,SAAS,qBAAhBvC,kBAAkB0C,MAAM,CAAC,CAACC,MAAMC,MAC9BC,KAAKC,GAAG,CAACF,MAAM7D,aAAa8D,KAAKC,GAAG,CAACH,OAAO5D,aAAa6D,MAAMD,UAEjE5D;IAEF,IAAI,CAACiB,OAAO+C,mBAAmB,IAAIrE,IAAIsE,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAAS;QACxE,yDAAyD;QACzD,+CAA+C;QAC/C,OAAOvE;IACT;IAEA,OAAO,AAAGwE,IAAAA,kDAA0B,EAAClD,OAAOK,IAAI,IAAE,UAAO8C,mBACvDzE,OACA,QAAKuB,QAAM,QAAKwC;AACpB;AAEA,MAAMW,UAAU,IAAI7D,IAGlB;IACA;QAAC;QAAW4B;KAAc;IAC1B;QAAC;QAASpB;KAAY;IACtB;QAAC;QAAcgB;KAAiB;IAChC;QAAC;QAAUD;KAAa;IACxB;QAAC;QAAUG;KAAa;CACzB;AAED,MAAMoC,sBAAsB;IAC1B;IACA;IACA;IACA;IACAvD;CACD;AA+BD,SAASwD,gBACP5E,GAAoC;IAEpC,OAAO,AAACA,IAAsB6E,OAAO,KAAKzD;AAC5C;AAEA,SAAS0D,kBACP9E,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKoB;AAC1C;AAEA,SAAS2D,eAAe/E,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACd4E,CAAAA,gBAAgB5E,QACf8E,kBAAkB9E,IAAmB;AAE3C;AA8CA,SAASgF,UACP,KAAsC,EACtCzD,KAAyB,EACzB0D,MAAmB,EACnBC,KAAyB;IAHzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAKA,IAAIF,SAAUD,CAAAA,WAAW,UAAUA,WAAW,YAAW,GAAI;QAC3D,yDAAyD;QACzD,MAAMI,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAa1C,IAAI,CAAC6C,SAASF,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAazC,MAAM,EAAE;YACvB,MAAM6C,gBAAgBvB,KAAKwB,GAAG,IAAIL,gBAAgB;YAClD,OAAO;gBACLM,QAAQR,SAASS,MAAM,CAAC,CAACC,IAAMA,KAAKX,WAAW,CAAC,EAAE,GAAGO;gBACrDK,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQR;YAAUW,MAAM;QAAI;IACvC;IACA,IACE,OAAOxE,UAAU,YACjB0D,WAAW,UACXA,WAAW,cACX;QACA,OAAO;YAAEW,QAAQT;YAAaY,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAIjF,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACY;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACyE,GAAG,CACpC,CAACC,IAAMb,SAASc,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMb,QAAQ,CAACA,SAASvC,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAE+C;QAAQG,MAAM;IAAI;AAC7B;AAmBA,SAASK,iBAAiB,KASR;IATQ,IAAA,EACxB9E,MAAM,EACNtB,GAAG,EACHqG,WAAW,EACXpB,MAAM,EACN1D,KAAK,EACLC,OAAO,EACP0D,KAAK,EACLoB,MAAM,EACU,GATQ;IAUxB,IAAID,aAAa;QACf,OAAO;YAAErG;YAAKuG,QAAQnF;YAAW8D,OAAO9D;QAAU;IACpD;IAEA,MAAM,EAAEwE,MAAM,EAAEG,IAAI,EAAE,GAAGf,UAAU1D,QAAQC,OAAO0D,QAAQC;IAC1D,MAAMsB,OAAOZ,OAAO/C,MAAM,GAAG;IAE7B,OAAO;QACLqC,OAAO,CAACA,SAASa,SAAS,MAAM,UAAUb;QAC1CqB,QAAQX,OACLI,GAAG,CACF,CAACC,GAAGQ,IACF,AAAGH,OAAO;gBAAEhF;gBAAQtB;gBAAKwB;gBAASD,OAAO0E;YAAE,KAAG,MAC5CF,CAAAA,SAAS,MAAME,IAAIQ,IAAI,CAAA,IACtBV,MAEN/D,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDhC,KAAKsG,OAAO;YAAEhF;YAAQtB;YAAKwB;YAASD,OAAOqE,MAAM,CAACY,KAAK;QAAC;IAC1D;AACF;AAEA,SAASE,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOlB,SAASkB,GAAG;IACrB;IACA,OAAOvF;AACT;AAEA,SAASwF,mBAAmBC,WAAuC;QAC/CA;IAAlB,MAAMC,YAAYD,EAAAA,sBAAAA,YAAYvF,MAAM,qBAAlBuF,oBAAoBP,MAAM,KAAI;IAChD,MAAMS,OAAOrC,QAAQzC,GAAG,CAAC6E;IACzB,IAAIC,MAAM;QACR,OAAOA,KAAKF;IACd;IACA,MAAM,qBAIL,CAJK,IAAIrE,MACR,AAAC,2DAAwDwE,0BAAa,CAAChF,IAAI,CACzE,QACA,iBAAc8E,YAHZ,qBAAA;eAAA;oBAAA;sBAAA;IAIN;AACF;AAEA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASG,cACPC,GAA2B,EAC3BlH,GAAW,EACXiF,MAAmB,EACnBkC,WAA6B,EAC7BC,oBAA2E,EAC3EC,eAAqC;IAErC,IAAI,CAACH,OAAOA,IAAIlH,GAAG,KAAKe,gBAAgBmG,GAAG,CAAC,kBAAkB,KAAKlH,KAAK;QACtE;IACF;IACAkH,GAAG,CAAC,kBAAkB,GAAGlH;IACzB,MAAMmG,IAAI,YAAYe,MAAMA,IAAII,MAAM,KAAKC,QAAQC,OAAO;IAC1DrB,EAAEsB,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACR,IAAIS,UAAU,EAAE;YACnB,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACAjH,gBAAgBkH,GAAG,CAAC5H;QACpB,IAAImH,gBAAgB,QAAQ;YAC1BE,gBAAgB;QAClB;QACA,IAAID,wCAAAA,qBAAsBS,OAAO,EAAE;YACjC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGb;YACxC,mDAAmD;YACnD,sDAAsD;YACtDE,qBAAqBS,OAAO,CAAC;gBAAEC;gBAAcC;YAAc;QAC7D;QACA,IAAIxH,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;gBACrCwE;YAAJ,KAAIA,qBAAAA,IAAIc,aAAa,qBAAjBd,mBAAmBc,aAAa,EAAE;gBACpC,MAAMC,SAASC,iBAAiBhB,IAAIc,aAAa,CAACA,aAAa;gBAC/D,IAAI,CAACC,OAAOE,QAAQ,EAAE;gBACpB,sHAAsH;gBACxH,OAAO,IAAIlD,WAAW,gBAAgBgD,OAAOG,OAAO,KAAK,QAAQ;oBAC/DC,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI;gBAE3B,OAAO,IACLiF,WAAW,UACXgD,OAAOE,QAAQ,KAAK,cACpBF,OAAOE,QAAQ,KAAK,WACpBF,OAAOE,QAAQ,KAAK,YACpB;oBACAE,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI,6DAA0DiI,OAAOE,QAAQ,GAAC;gBAErG;YACF;QACF;IACF;AACF;AAEA,MAAMG,eAAe;QAAC,EACpBC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVzD,MAAM,EACN0D,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACN3B,WAAW,EACX4B,OAAO,EACPC,SAAS,EACT1H,MAAM,EACN+E,WAAW,EACXC,MAAM,EACNc,oBAAoB,EACpBC,eAAe,EACf4B,eAAe,EACfC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,aAAa,EACb,GAAGC,MACe;IAClBP,UAAUD,SAAS,SAASC;IAC5B,qBACE;;0BACE,qBAAC7B;gBACE,GAAGoC,IAAI;gBACP,GAAGf,aAAa;gBACjBgB,UAAS;gBACTC,aAAWvE;gBACX0D,WAAWA;gBACXc,OAAO;oBAAE,GAAGb,QAAQ;oBAAE,GAAGC,SAAS;gBAAC;gBACnCa,KAAKC,IAAAA,kBAAW,EACd,CAACzC;oBACC,IAAI3G,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;wBACzC,IAAIwE,OAAO,CAAC8B,WAAW;4BACrBvF,QAAQC,KAAK,CAAE,6CAA4CwD;wBAC7D;oBACF;oBACA+B,gBAAgB/B;oBAChB,IAAIA,uBAAAA,IAAK0C,QAAQ,EAAE;wBACjB3C,cACEC,KACA8B,WACA/D,QACAkC,aACAC,sBACAC;oBAEJ;gBACF,GACA;oBACE4B;oBACAD;oBACA/D;oBACAkC;oBACAC;oBACAC;iBACD;gBAEH6B,QAAQ,CAACW;oBACP,MAAM3C,MAAM2C,MAAMC,aAAa;oBAC/B7C,cACEC,KACA8B,WACA/D,QACAkC,aACAC,sBACAC;oBAEF,IAAI6B,QAAQ;wBACVA,OAAOW;oBACT;gBACF;gBACAV,SAAS,CAACU;oBACR,IAAI1C,gBAAgB,QAAQ;wBAC1B,2EAA2E;wBAC3EE,gBAAgB;oBAClB;oBACA,IAAI8B,SAAS;wBACXA,QAAQU;oBACV;gBACF;;YAEAf,CAAAA,UAAU3B,gBAAgB,MAAK,mBAC/B,qBAAC4C;0BACC,cAAA,qBAAC7C;oBACE,GAAGoC,IAAI;oBACR,kDAAkD;oBAClDP,SAASA;oBACTQ,UAAS;oBACTC,aAAWvE;oBACXwE,OAAOb;oBACPD,WAAWA;oBAIV,GAAGvC,iBAAiB;wBACnB9E;wBACAtB,KAAKgJ;wBACL3C;wBACApB;wBACA1D,OAAOkH;wBACPjH,SAASkH;wBACTxD,OAAOmE;wBACP/C;oBACF,EAAE;;;;;AAMd;AAEe,SAASxG,MAAM,KAmBjB;IAnBiB,IAAA,EAC5BE,GAAG,EACHkF,KAAK,EACLmB,cAAc,KAAK,EACnB2D,WAAW,KAAK,EAChBjB,OAAO,EACPkB,WAAW,IAAI,EACfC,YAAY,EACZvB,SAAS,EACTnH,OAAO,EACPD,KAAK,EACL4I,MAAM,EACNV,KAAK,EACLW,SAAS,EACTC,cAAc,EACdC,iBAAiB,EACjBnD,cAAc,OAAO,EACrBoD,WAAW,EACX,GAAGC,KACQ,GAnBiB;IAoB5B,MAAMC,gBAAgBC,IAAAA,iBAAU,EAACC,mDAAkB;IACnD,MAAMrJ,SAAsBsJ,IAAAA,cAAO,EAAC;YAIhBC;QAHlB,MAAMA,IAAIvK,aAAamK,iBAAiBK,+BAAkB;QAC1D,MAAM1F,WAAW;eAAIyF,EAAE1F,WAAW;eAAK0F,EAAEE,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAM/F,cAAc0F,EAAE1F,WAAW,CAAC6F,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAMrH,aAAYgH,eAAAA,EAAEhH,SAAS,qBAAXgH,aAAaG,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGL,CAAC;YAAEzF;YAAUD;YAAatB;QAAU;IAClD,GAAG;QAAC4G;KAAc;IAElB,IAAInB,OAA4BkB;IAChC,IAAIvF,SAAmCC,QAAQ,eAAe;IAC9D,IAAI,YAAYoE,MAAM;QACpB,qDAAqD;QACrD,IAAIA,KAAKrE,MAAM,EAAEA,SAASqE,KAAKrE,MAAM;QAErC,+CAA+C;QAC/C,OAAOqE,KAAKrE,MAAM;IACpB;IAEA,IAAIqB,SAAgCM;IACpC,IAAI,YAAY0C,MAAM;QACpB,IAAIA,KAAKhD,MAAM,EAAE;YACf,MAAM6E,oBAAoB7B,KAAKhD,MAAM;YACrCA,SAAS,CAAC8E;gBACR,MAAM,EAAE9J,QAAQ+J,CAAC,EAAE,GAAGC,MAAM,GAAGF;gBAC/B,gDAAgD;gBAChD,2CAA2C;gBAC3C,OAAOD,kBAAkBG;YAC3B;QACF;QACA,8CAA8C;QAC9C,OAAOhC,KAAKhD,MAAM;IACpB;IAEA,IAAIiF,YAAY;IAChB,IAAIxG,eAAe/E,MAAM;QACvB,MAAMwL,kBAAkB5G,gBAAgB5E,OAAOA,IAAI6E,OAAO,GAAG7E;QAE7D,IAAI,CAACwL,gBAAgBxL,GAAG,EAAE;YACxB,MAAM,qBAIL,CAJK,IAAIwC,MACR,AAAC,gJAA6IM,KAAKC,SAAS,CAC1JyI,mBAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QACAjB,cAAcA,eAAeiB,gBAAgBjB,WAAW;QACxDgB,YAAYC,gBAAgBxL,GAAG;QAC/B,IAAI,CAACiF,UAAUA,WAAW,QAAQ;YAChCkF,SAASA,UAAUqB,gBAAgBrB,MAAM;YACzC5I,QAAQA,SAASiK,gBAAgBjK,KAAK;YACtC,IAAI,CAACiK,gBAAgBrB,MAAM,IAAI,CAACqB,gBAAgBjK,KAAK,EAAE;gBACrD,MAAM,qBAIL,CAJK,IAAIiB,MACR,AAAC,6JAA0JM,KAAKC,SAAS,CACvKyI,mBAFE,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF;IACF;IACAxL,MAAM,OAAOA,QAAQ,WAAWA,MAAMuL;IAEtC,IAAIzC,SACF,CAACkB,YAAajB,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI/I,IAAIgD,UAAU,CAAC,YAAYhD,IAAIgD,UAAU,CAAC,UAAU;QACtD,uEAAuE;QACvEqD,cAAc;QACdyC,SAAS;IACX;IACA,IAAI,OAAO9H,WAAW,eAAeN,gBAAgB+K,GAAG,CAACzL,MAAM;QAC7D8I,SAAS;IACX;IACA,IAAIxH,OAAO+E,WAAW,EAAE;QACtBA,cAAc;IAChB;IAEA,MAAM,CAACqF,cAAcrE,gBAAgB,GAAGsE,IAAAA,eAAQ,EAAC;IACjD,MAAM,CAAC1C,iBAAiB2C,eAAeC,iBAAiB,GACtDC,IAAAA,gCAAe,EAAmB;QAChCC,SAAS9B;QACT+B,YAAY9B,gBAAgB;QAC5B+B,UAAU,CAACnD;IACb;IACF,MAAMM,YAAY,CAACN,UAAU8C;IAE7B,MAAMM,eAAuD;QAC3DC,WAAW;QACX/D,SAAS;QACTgE,UAAU;QACV7K,OAAO;QACP4I,QAAQ;QACRkC,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,MAAMC,aAAqD;QACzDP,WAAW;QACX/D,SAAS;QACT7G,OAAO;QACP4I,QAAQ;QACRkC,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,IAAIE,WAAW;IACf,IAAIC;IACJ,MAAMC,cAA+B;QACnC1E,UAAU;QACV2E,KAAK;QACLC,MAAM;QACNC,QAAQ;QACRC,OAAO;QAEPd,WAAW;QACXM,SAAS;QACTF,QAAQ;QACRC,QAAQ;QAERpE,SAAS;QACT7G,OAAO;QACP4I,QAAQ;QACR+C,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,WAAW;QAEXjD;QACAC;IACF;IAEA,IAAI5B,WAAW/B,OAAOnF;IACtB,IAAIiH,YAAY9B,OAAOyD;IACvB,MAAMzB,aAAahC,OAAOlF;IAE1B,IAAIjB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,CAAC1C,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CyI,WAAWA,YAAY;YACvBD,YAAYA,aAAa;YACzBnC,cAAc;QAChB,OAAO;YACL,IAAI,CAAC1B,oBAAoBb,QAAQ,CAACmB,SAAS;gBACzC,MAAM,qBAIL,CAJK,IAAIzC,MACR,AAAC,qBAAkBxC,MAAI,gDAA6CiF,SAAO,wBAAqBN,oBAAoBqB,GAAG,CACrHsH,QACAtL,IAAI,CAAC,OAAK,MAHR,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YAEA,IACE,AAAC,OAAOyG,aAAa,eAAe8E,MAAM9E,aACzC,OAAOD,cAAc,eAAe+E,MAAM/E,YAC3C;gBACA,MAAM,qBAEL,CAFK,IAAIhG,MACR,AAAC,qBAAkBxC,MAAI,gFADnB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAIiF,WAAW,UAAW1D,CAAAA,SAAS4I,MAAK,GAAI;gBAC1C9B,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI;YAE3B;YACA,IAAI,CAACmB,qBAAqB2C,QAAQ,CAACiF,UAAU;gBAC3C,MAAM,qBAIL,CAJK,IAAIvG,MACR,AAAC,qBAAkBxC,MAAI,iDAA8C+I,UAAQ,wBAAqB5H,qBAAqB6E,GAAG,CACxHsH,QACAtL,IAAI,CAAC,OAAK,MAHR,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YACA,IAAIgI,YAAYjB,YAAY,QAAQ;gBAClC,MAAM,qBAEL,CAFK,IAAIvG,MACR,AAAC,qBAAkBxC,MAAI,sFADnB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAIkF,SAASD,WAAW,UAAUA,WAAW,cAAc;gBACzDoD,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI;YAE3B;YACA,IAAImH,gBAAgB,QAAQ;gBAC1B,IAAIlC,WAAW,UAAU,AAACwD,CAAAA,YAAY,CAAA,IAAMD,CAAAA,aAAa,CAAA,IAAK,MAAM;oBAClEH,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI;gBAE3B;gBACA,IAAI,CAACuK,aAAa;oBAChB,MAAMiD,iBAAiB;wBAAC;wBAAQ;wBAAO;wBAAQ;qBAAO,CAAC,iCAAiC;;oBAExF,MAAM,qBASL,CATK,IAAIhL,MACR,AAAC,qBAAkBxC,MAAI,mUAGgEwN,eAAexL,IAAI,CACxG,OACA,mMANE,qBAAA;+BAAA;oCAAA;sCAAA;oBASN;gBACF;YACF;YACA,IAAI,SAASsH,MAAM;gBACjBjB,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI;YAE3B;YAEA,IAAI,CAACqG,eAAeC,WAAWM,oBAAoB;gBACjD,MAAM6G,SAASnH,OAAO;oBACpBhF;oBACAtB;oBACAuB,OAAOkH,YAAY;oBACnBjH,SAASkH,cAAc;gBACzB;gBACA,IAAIjH;gBACJ,IAAI;oBACFA,MAAM,IAAIC,IAAI+L;gBAChB,EAAE,OAAOjK,KAAK,CAAC;gBACf,IAAIiK,WAAWzN,OAAQyB,OAAOA,IAAIiM,QAAQ,KAAK1N,OAAO,CAACyB,IAAIkM,MAAM,EAAG;oBAClEtF,IAAAA,kBAAQ,EACN,AAAC,qBAAkBrI,MAAI,4HACpB;gBAEP;YACF;YAEA,IAAIyJ,OAAO;gBACT,IAAImE,oBAAoBC,OAAOC,IAAI,CAACrE,OAAO5D,MAAM,CAC/C,CAACkI,MAAQA,OAAOlB;gBAElB,IAAIe,kBAAkB/K,MAAM,EAAE;oBAC5BwF,IAAAA,kBAAQ,EACN,AAAC,oBAAiBrI,MAAI,iGAA8F4N,kBAAkB5L,IAAI,CACxI;gBAGN;YACF;YAEA,IACE,OAAOhB,WAAW,eAClB,CAACF,gBACDE,OAAOgN,mBAAmB,EAC1B;gBACAlN,eAAe,IAAIkN,oBAAoB,CAACC;oBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;4BAE3BD;wBADf,0EAA0E;wBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgBlO,GAAG,KAAI;wBACtC,MAAMsO,WAAW1N,QAAQqB,GAAG,CAACmM;wBAC7B,IACEE,YACA,CAACA,SAAStE,QAAQ,IAClBsE,SAASnH,WAAW,KAAK,UACzB,CAACmH,SAAStO,GAAG,CAACgD,UAAU,CAAC,YACzB,CAACsL,SAAStO,GAAG,CAACgD,UAAU,CAAC,UACzB;4BACA,iDAAiD;4BACjDqF,IAAAA,kBAAQ,EACN,AAAC,qBAAkBiG,SAAStO,GAAG,GAAC,8HAC7B;wBAEP;oBACF;gBACF;gBACA,IAAI;oBACFc,aAAayN,OAAO,CAAC;wBACnBC,MAAM;wBACNC,UAAU;oBACZ;gBACF,EAAE,OAAOjL,KAAK;oBACZ,oCAAoC;oBACpCC,QAAQC,KAAK,CAACF;gBAChB;YACF;QACF;IACF;IACA,MAAMoF,WAAWiF,OAAOa,MAAM,CAAC,CAAC,GAAGjF,OAAOoD;IAC1C,MAAMhE,YACJ1B,gBAAgB,UAAU,CAACuE,eACvB;QACEiD,gBAAgBvE,aAAa;QAC7BwE,oBAAoBvE,kBAAkB;QACtCxE,QAAQ;QACRgJ,iBAAiB,AAAC,UAAOtE,cAAY;IACvC,IACA,CAAC;IACP,IAAItF,WAAW,QAAQ;QACrB,sCAAsC;QACtCiH,aAAa9D,OAAO,GAAG;QACvB8D,aAAa/D,QAAQ,GAAG;QACxB+D,aAAaY,GAAG,GAAG;QACnBZ,aAAaa,IAAI,GAAG;QACpBb,aAAac,MAAM,GAAG;QACtBd,aAAae,KAAK,GAAG;IACvB,OAAO,IACL,OAAOxE,aAAa,eACpB,OAAOD,cAAc,aACrB;QACA,iDAAiD;QACjD,MAAMsG,WAAWtG,YAAYC;QAC7B,MAAMsG,aAAaxB,MAAMuB,YAAY,SAAS,AAAC,KAAEA,WAAW,MAAI;QAChE,IAAI7J,WAAW,cAAc;YAC3B,qEAAqE;YACrEiH,aAAa9D,OAAO,GAAG;YACvB8D,aAAa/D,QAAQ,GAAG;YACxBwE,WAAW;YACXD,WAAWqC,UAAU,GAAGA;QAC1B,OAAO,IAAI9J,WAAW,aAAa;YACjC,oEAAoE;YACpEiH,aAAa9D,OAAO,GAAG;YACvB8D,aAAa/D,QAAQ,GAAG;YACxB+D,aAAaiB,QAAQ,GAAG;YACxBR,WAAW;YACXD,WAAWS,QAAQ,GAAG;YACtBP,cAAc,AAAC,uGAAoGnE,WAAS,qBAAkBD,YAAU;QAC1J,OAAO,IAAIvD,WAAW,SAAS;YAC7B,gEAAgE;YAChEiH,aAAa9D,OAAO,GAAG;YACvB8D,aAAa/D,QAAQ,GAAG;YACxB+D,aAAa3K,KAAK,GAAGkH;YACrByD,aAAa/B,MAAM,GAAG3B;QACxB;IACF,OAAO;QACL,wBAAwB;QACxB,IAAIjI,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,MAAM,qBAEL,CAFK,IAAIF,MACR,AAAC,qBAAkBxC,MAAI,8EADnB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAIuI,gBAAmC;QACrCvI,KAAKe;QACLwF,QAAQnF;QACR8D,OAAO9D;IACT;IAEA,IAAIgI,WAAW;QACbb,gBAAgBnC,iBAAiB;YAC/B9E;YACAtB;YACAqG;YACApB;YACA1D,OAAOkH;YACPjH,SAASkH;YACTxD;YACAoB;QACF;IACF;IAEA,IAAI0C,YAAoBhJ;IAExB,IAAIO,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAO1B,WAAW,aAAa;YACjC,IAAIgO;YACJ,IAAI;gBACFA,UAAU,IAAItN,IAAI6G,cAAcvI,GAAG;YACrC,EAAE,OAAOiP,GAAG;gBACVD,UAAU,IAAItN,IAAI6G,cAAcvI,GAAG,EAAEgB,OAAOkO,QAAQ,CAAC/M,IAAI;YAC3D;YACAvB,QAAQkB,GAAG,CAACkN,QAAQ7M,IAAI,EAAE;gBAAEnC;gBAAKgK;gBAAU7C;YAAY;QACzD;IACF;IAEA,MAAMgI,YAKUjP,gBACZkB,YACA;QACEgO,aAAa7G,cAAchC,MAAM;QACjCwE,YAAYxC,cAAcrD,KAAK;QAC/BmK,aAAa/F,KAAK+F,WAAW;QAC7BC,gBAAgBhG,KAAKgG,cAAc;IACrC;IAEJ,MAAMC,kBACJ,OAAOvO,WAAW,cAAcwO,cAAK,CAACC,SAAS,GAAGD,cAAK,CAACD,eAAe;IACzE,MAAMnI,uBAAuBsI,IAAAA,aAAM,EAACpF;IAEpC,MAAMqF,mBAAmBD,IAAAA,aAAM,EAAwB1P;IACvDyP,IAAAA,gBAAS,EAAC;QACRrI,qBAAqBS,OAAO,GAAGyC;IACjC,GAAG;QAACA;KAAkB;IAEtBiF,gBAAgB;QACd,IAAII,iBAAiB9H,OAAO,KAAK7H,KAAK;YACpC6L;YACA8D,iBAAiB9H,OAAO,GAAG7H;QAC7B;IACF,GAAG;QAAC6L;QAAkB7L;KAAI;IAE1B,MAAM4P,iBAAiB;QACrB9G;QACAP;QACAC;QACAC;QACAC;QACAzD;QACA0D;QACAC;QACAC;QACAE;QACAzH;QACA+E;QACAc;QACAb;QACA0C;QACA5B;QACAC;QACA4B;QACAG;QACAC,eAAenE;QACf,GAAGoE,IAAI;IACT;IACA,qBACE;;0BACE,sBAACuG;gBAAKpG,OAAOyC;;oBACVS,yBACC,qBAACkD;wBAAKpG,OAAOiD;kCACVE,4BACC,qBAAC1F;4BACCuC,OAAO;gCACLrB,SAAS;gCACT+E,UAAU;gCACV5L,OAAO;gCACP4I,QAAQ;gCACRkC,YAAY;gCACZC,SAAS;gCACTC,QAAQ;gCACRC,QAAQ;gCACRC,SAAS;4BACX;4BACAqD,KAAI;4BACJC,eAAa;4BACb/P,KAAK4M;6BAEL;yBAEJ;kCACJ,qBAACtE;wBAAc,GAAGsH,cAAc;;;;YAEjC,CAAC1P,iBAAiB8J,WACjB,sEAAsE;YACtE,qEAAqE;YACrE,6DAA6D;YAC7D,EAAE;YACF,8EAA8E;0BAC9E,qBAACgG,aAAI;0BACH,cAAA,qBAACC;oBAOCC,KAAI;oBACJC,IAAG;oBACHhO,MAAMoG,cAAchC,MAAM,GAAGnF,YAAYmH,cAAcvI,GAAG;oBACzD,GAAGmP,SAAS;mBARX,YACA5G,cAAcvI,GAAG,GACjBuI,cAAchC,MAAM,GACpBgC,cAAcrD,KAAK;iBAQvB;;;AAGV"}