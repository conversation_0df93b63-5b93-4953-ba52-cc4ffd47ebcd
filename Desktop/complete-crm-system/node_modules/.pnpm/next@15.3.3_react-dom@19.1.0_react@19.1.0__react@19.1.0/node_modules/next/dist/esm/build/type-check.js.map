{"version": 3, "sources": ["../../src/build/type-check.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\nimport type { Telemetry } from '../telemetry/storage'\nimport type { Span } from '../trace'\n\nimport path from 'path'\nimport * as Log from './output/log'\nimport { Worker } from '../lib/worker'\nimport { verifyAndLint } from '../lib/verifyAndLint'\nimport createSpinner from './spinner'\nimport { eventTypeCheckCompleted } from '../telemetry/events'\nimport isError from '../lib/is-error'\n\n/**\n * typescript will be loaded in \"next/lib/verify-typescript-setup\" and\n * then passed to \"next/lib/typescript/runTypeCheck\" as a parameter.\n *\n * Since it is impossible to pass a function from main thread to a worker,\n * instead of running \"next/lib/typescript/runTypeCheck\" in a worker,\n * we will run entire \"next/lib/verify-typescript-setup\" in a worker instead.\n */\nfunction verifyTypeScriptSetup(\n  dir: string,\n  distDir: string,\n  intentDirs: string[],\n  typeCheckPreflight: boolean,\n  tsconfigPath: string,\n  disableStaticImages: boolean,\n  cacheDir: string | undefined,\n  enableWorkerThreads: boolean | undefined,\n  hasAppDir: boolean,\n  hasPagesDir: boolean\n) {\n  const typeCheckWorker = new Worker(\n    require.resolve('../lib/verify-typescript-setup'),\n    {\n      exposedMethods: ['verifyTypeScriptSetup'],\n      numWorkers: 1,\n      enableWorkerThreads,\n      maxRetries: 0,\n    }\n  ) as Worker & {\n    verifyTypeScriptSetup: typeof import('../lib/verify-typescript-setup').verifyTypeScriptSetup\n  }\n\n  return typeCheckWorker\n    .verifyTypeScriptSetup({\n      dir,\n      distDir,\n      intentDirs,\n      typeCheckPreflight,\n      tsconfigPath,\n      disableStaticImages,\n      cacheDir,\n      hasAppDir,\n      hasPagesDir,\n    })\n    .then((result) => {\n      typeCheckWorker.end()\n      return result\n    })\n    .catch(() => {\n      // The error is already logged in the worker, we simply exit the main thread to prevent the\n      // `Jest worker encountered 1 child process exceptions, exceeding retry limit` from showing up\n      process.exit(1)\n    })\n}\n\nexport async function startTypeChecking({\n  cacheDir,\n  config,\n  dir,\n  ignoreESLint,\n  nextBuildSpan,\n  pagesDir,\n  runLint,\n  shouldLint,\n  telemetry,\n  appDir,\n}: {\n  cacheDir: string\n  config: NextConfigComplete\n  dir: string\n  ignoreESLint: boolean\n  nextBuildSpan: Span\n  pagesDir?: string\n  runLint: boolean\n  shouldLint: boolean\n  telemetry: Telemetry\n  appDir?: string\n}) {\n  const ignoreTypeScriptErrors = Boolean(config.typescript.ignoreBuildErrors)\n\n  const eslintCacheDir = path.join(cacheDir, 'eslint/')\n\n  if (ignoreTypeScriptErrors) {\n    Log.info('Skipping validation of types')\n  }\n  if (runLint && ignoreESLint) {\n    // only print log when build require lint while ignoreESLint is enabled\n    Log.info('Skipping linting')\n  }\n\n  let typeCheckingAndLintingSpinnerPrefixText: string | undefined\n  let typeCheckingAndLintingSpinner:\n    | ReturnType<typeof createSpinner>\n    | undefined\n\n  if (!ignoreTypeScriptErrors && shouldLint) {\n    typeCheckingAndLintingSpinnerPrefixText =\n      'Linting and checking validity of types'\n  } else if (!ignoreTypeScriptErrors) {\n    typeCheckingAndLintingSpinnerPrefixText = 'Checking validity of types'\n  } else if (shouldLint) {\n    typeCheckingAndLintingSpinnerPrefixText = 'Linting'\n  }\n\n  // we will not create a spinner if both ignoreTypeScriptErrors and ignoreESLint are\n  // enabled, but we will still verifying project's tsconfig and dependencies.\n  if (typeCheckingAndLintingSpinnerPrefixText) {\n    typeCheckingAndLintingSpinner = createSpinner(\n      typeCheckingAndLintingSpinnerPrefixText\n    )\n  }\n\n  const typeCheckStart = process.hrtime()\n\n  try {\n    const [[verifyResult, typeCheckEnd]] = await Promise.all([\n      nextBuildSpan.traceChild('verify-typescript-setup').traceAsyncFn(() =>\n        verifyTypeScriptSetup(\n          dir,\n          config.distDir,\n          [pagesDir, appDir].filter(Boolean) as string[],\n          !ignoreTypeScriptErrors,\n          config.typescript.tsconfigPath,\n          config.images.disableStaticImages,\n          cacheDir,\n          config.experimental.workerThreads,\n          !!appDir,\n          !!pagesDir\n        ).then((resolved) => {\n          const checkEnd = process.hrtime(typeCheckStart)\n          return [resolved, checkEnd] as const\n        })\n      ),\n      shouldLint &&\n        nextBuildSpan.traceChild('verify-and-lint').traceAsyncFn(async () => {\n          await verifyAndLint(\n            dir,\n            eslintCacheDir,\n            config.eslint?.dirs,\n            config.experimental.workerThreads,\n            telemetry\n          )\n        }),\n    ])\n    typeCheckingAndLintingSpinner?.stopAndPersist()\n\n    if (!ignoreTypeScriptErrors && verifyResult) {\n      telemetry.record(\n        eventTypeCheckCompleted({\n          durationInSeconds: typeCheckEnd[0],\n          typescriptVersion: verifyResult.version,\n          inputFilesCount: verifyResult.result?.inputFilesCount,\n          totalFilesCount: verifyResult.result?.totalFilesCount,\n          incremental: verifyResult.result?.incremental,\n        })\n      )\n    }\n  } catch (err) {\n    // prevent showing jest-worker internal error as it\n    // isn't helpful for users and clutters output\n    if (isError(err) && err.message === 'Call retries were exceeded') {\n      await telemetry.flush()\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": ["path", "Log", "Worker", "verifyAndLint", "createSpinner", "eventTypeCheckCompleted", "isError", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "then", "result", "end", "catch", "process", "exit", "startTypeChecking", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "join", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "eslint", "dirs", "stopAndPersist", "record", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "message", "flush"], "mappings": "AAIA,OAAOA,UAAU,OAAM;AACvB,YAAYC,SAAS,eAAc;AACnC,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,uBAAsB;AACpD,OAAOC,mBAAmB,YAAW;AACrC,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,OAAOC,aAAa,kBAAiB;AAErC;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIhB,OAC1BiB,QAAQC,OAAO,CAAC,mCAChB;QACEC,gBAAgB;YAAC;SAAwB;QACzCC,YAAY;QACZP;QACAQ,YAAY;IACd;IAKF,OAAOL,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCO,IAAI,CAAC,CAACC;QACLP,gBAAgBQ,GAAG;QACnB,OAAOD;IACT,GACCE,KAAK,CAAC;QACL,2FAA2F;QAC3F,8FAA8F;QAC9FC,QAAQC,IAAI,CAAC;IACf;AACJ;AAEA,OAAO,eAAeC,kBAAkB,EACtChB,QAAQ,EACRiB,MAAM,EACNvB,GAAG,EACHwB,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiB3C,KAAK4C,IAAI,CAAC9B,UAAU;IAE3C,IAAIyB,wBAAwB;QAC1BtC,IAAI4C,IAAI,CAAC;IACX;IACA,IAAIV,WAAWH,cAAc;QAC3B,uEAAuE;QACvE/B,IAAI4C,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACR,0BAA0BH,YAAY;QACzCU,0CACE;IACJ,OAAO,IAAI,CAACP,wBAAwB;QAClCO,0CAA0C;IAC5C,OAAO,IAAIV,YAAY;QACrBU,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgC3C,cAC9B0C;IAEJ;IAEA,MAAME,iBAAiBpB,QAAQqB,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDpB,cAAcqB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DhD,sBACEC,KACAuB,OAAOtB,OAAO,EACd;oBAACyB;oBAAUI;iBAAO,CAACkB,MAAM,CAAChB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAAC7B,YAAY,EAC9BmB,OAAO0B,MAAM,CAAC5C,mBAAmB,EACjCC,UACAiB,OAAO2B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACrB,QACF,CAAC,CAACJ,UACFV,IAAI,CAAC,CAACoC;oBACN,MAAMC,WAAWjC,QAAQqB,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEFzB,cACEH,cAAcqB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrDxB;gBAHF,MAAM5B,cACJK,KACAmC,iBACAZ,iBAAAA,OAAO+B,MAAM,qBAAb/B,eAAegC,IAAI,EACnBhC,OAAO2B,YAAY,CAACC,aAAa,EACjCtB;YAEJ;SACH;QACDU,iDAAAA,8BAA+BiB,cAAc;QAE7C,IAAI,CAACzB,0BAA0BW,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBb,UAAU4B,MAAM,CACd5D,wBAAwB;gBACtB6D,mBAAmBf,YAAY,CAAC,EAAE;gBAClCgB,mBAAmBjB,aAAakB,OAAO;gBACvCC,eAAe,GAAEnB,uBAAAA,aAAazB,MAAM,qBAAnByB,qBAAqBmB,eAAe;gBACrDC,eAAe,GAAEpB,wBAAAA,aAAazB,MAAM,qBAAnByB,sBAAqBoB,eAAe;gBACrDC,WAAW,GAAErB,wBAAAA,aAAazB,MAAM,qBAAnByB,sBAAqBqB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAIlE,QAAQkE,QAAQA,IAAIC,OAAO,KAAK,8BAA8B;YAChE,MAAMpC,UAAUqC,KAAK;YACrB9C,QAAQC,IAAI,CAAC;QACf;QACA,MAAM2C;IACR;AACF"}