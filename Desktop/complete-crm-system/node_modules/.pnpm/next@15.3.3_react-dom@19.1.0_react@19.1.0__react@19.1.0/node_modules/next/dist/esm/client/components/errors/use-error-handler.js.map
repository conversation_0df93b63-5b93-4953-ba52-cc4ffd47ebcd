{"version": 3, "sources": ["../../../../src/client/components/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs, parseConsoleArgs } from '../../lib/console'\nimport isError from '../../../lib/is-error'\nimport { createConsoleError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from '../errors/stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleConsoleError(\n  originError: unknown,\n  consoleErrorArgs: any[]\n) {\n  let error: Error\n  const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n  if (isError(originError)) {\n    error = createConsoleError(originError, environmentName)\n  } else {\n    error = createConsoleError(\n      formatConsoleArgs(consoleErrorArgs),\n      environmentName\n    )\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function handleClientError(originError: unknown) {\n  let error: Error\n  if (isError(originError)) {\n    error = originError\n  } else {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = originError + ''\n    error = new Error(formattedErrorMessage)\n  }\n  error = getReactStitchedError(error)\n\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (event.error) {\n    handleClientError(event.error)\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = new Error(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "attachHydrationErrorState", "isNextRouterError", "storeHydrationErrorStateFromConsoleArgs", "formatConsoleArgs", "parseConsoleArgs", "isError", "createConsoleError", "enqueueConsecutiveDedupedError", "getReactStitchedError", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleConsoleError", "originError", "consoleErrorArgs", "error", "environmentName", "handler", "handleClientError", "formattedErrorMessage", "Error", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "push", "splice", "indexOf", "length", "onUnhandledError", "event", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "stackTraceLimit", "addEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,yBAAyB,QAAQ,iCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,uCAAuC,QAAQ,yBAAwB;AAChF,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAmB;AACvE,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,qBAAqB,QAAQ,2BAA0B;AAEhE,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAEjD,OAAO,SAASC,mBACdC,WAAoB,EACpBC,gBAAuB;IAEvB,IAAIC;IACJ,MAAM,EAAEC,eAAe,EAAE,GAAGpB,iBAAiBkB;IAC7C,IAAIjB,QAAQgB,cAAc;QACxBE,QAAQjB,mBAAmBe,aAAaG;IAC1C,OAAO;QACLD,QAAQjB,mBACNH,kBAAkBmB,mBAClBE;IAEJ;IACAD,QAAQf,sBAAsBe;IAE9BrB,2CAA2CoB;IAC3CtB,0BAA0BuB;IAE1BhB,+BAA+BS,YAAYO;IAC3C,KAAK,MAAME,WAAWR,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbgB,QAAQF;QACV;IACF;AACF;AAEA,OAAO,SAASG,kBAAkBL,WAAoB;IACpD,IAAIE;IACJ,IAAIlB,QAAQgB,cAAc;QACxBE,QAAQF;IACV,OAAO;QACL,sDAAsD;QACtD,MAAMM,wBAAwBN,cAAc;QAC5CE,QAAQ,qBAAgC,CAAhC,IAAIK,MAAMD,wBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+B;IACzC;IACAJ,QAAQf,sBAAsBe;IAE9BvB,0BAA0BuB;IAE1BhB,+BAA+BS,YAAYO;IAC3C,KAAK,MAAME,WAAWR,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbgB,QAAQF;QACV;IACF;AACF;AAEA,OAAO,SAASM,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExChC,UAAU;QACR,wBAAwB;QACxBiB,WAAWgB,OAAO,CAACF;QACnBZ,eAAec,OAAO,CAACD;QAEvB,wBAAwB;QACxBd,cAAcgB,IAAI,CAACH;QACnBX,kBAAkBc,IAAI,CAACF;QAEvB,OAAO;YACL,oBAAoB;YACpBd,cAAciB,MAAM,CAACjB,cAAckB,OAAO,CAACL,yBAAyB;YACpEX,kBAAkBe,MAAM,CACtBf,kBAAkBgB,OAAO,CAACJ,6BAC1B;YAGF,sBAAsB;YACtBf,WAAWkB,MAAM,CAAC,GAAGlB,WAAWoB,MAAM;YACtClB,eAAegB,MAAM,CAAC,GAAGhB,eAAekB,MAAM;QAChD;IACF,GAAG;QAACN;QAAwBC;KAA2B;AACzD;AAEA,SAASM,iBAAiBC,KAA8B;IACtD,IAAIrC,kBAAkBqC,MAAMf,KAAK,GAAG;QAClCe,MAAMC,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAID,MAAMf,KAAK,EAAE;QACfG,kBAAkBY,MAAMf,KAAK;IAC/B;AACF;AAEA,SAASiB,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIzC,kBAAkByC,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIhB,QAAQmB;IACZ,IAAInB,SAAS,CAAClB,QAAQkB,QAAQ;QAC5BA,QAAQ,qBAAqB,CAArB,IAAIK,MAAML,QAAQ,KAAlB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoB;IAC9B;IAEAL,eAAee,IAAI,CAACV;IACpB,KAAK,MAAME,WAAWN,kBAAmB;QACvCM,QAAQF;IACV;AACF;AAEA,OAAO,SAASoB;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDhB,MAAMiB,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETD,OAAOE,gBAAgB,CAAC,SAAST;QACjCO,OAAOE,gBAAgB,CAAC,sBAAsBN;IAChD;AACF"}