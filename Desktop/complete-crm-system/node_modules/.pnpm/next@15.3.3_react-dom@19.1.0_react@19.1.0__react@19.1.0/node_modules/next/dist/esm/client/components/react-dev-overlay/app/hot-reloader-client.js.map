{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport type { ReactNode } from 'react'\nimport { useCallback, useEffect, startTransition, useMemo, useRef } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { useRouter } from '../../navigation'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEBUG_INFO,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n  REACT_REFRESH_FULL_RELOAD,\n  reportInvalidHmrMessage,\n  useErrorOverlayReducer,\n} from '../shared'\nimport { parseStack } from '../utils/parse-stack'\nimport { AppDevOverlay } from './app-dev-overlay'\nimport { useError<PERSON>and<PERSON> } from '../../errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from '../utils/use-websocket'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport type { DebugInfo } from '../types'\nimport { useUntrackedPathname } from '../../navigation-untracked'\nimport { getReactStitchedError } from '../../errors/stitched-error'\nimport { handleDevBuildIndicatorHmrEvents } from '../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events'\nimport type { GlobalErrorComponent } from '../../error-boundary'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\nimport reportHmrLatency from '../utils/report-hmr-latency'\nimport { TurbopackHmr } from '../utils/turbopack-hot-reloader-common'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../app-router-headers'\n\nexport interface Dispatcher {\n  onBuildOk(): void\n  onBuildError(message: string): void\n  onVersionInfo(versionInfo: VersionInfo): void\n  onDebugInfo(debugInfo: DebugInfo): void\n  onBeforeRefresh(): void\n  onRefresh(): void\n  onStaticIndicator(status: boolean): void\n  onDevIndicator(devIndicator: DevIndicatorServerState): void\n}\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack(\n  sendMessage: (message: string) => void,\n  dispatcher: Dispatcher\n) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [], webpackStartMsSinceEpoch!, Date.now())\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack(sendMessage, dispatcher)\n      return\n    }\n\n    dispatcher.onRefresh()\n    resolvePendingHotUpdateWebpack()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the server for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  dispatcher: Dispatcher,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      const hmrUpdate = turbopackHmr!.onBuilt()\n      if (hmrUpdate != null) {\n        reportHmrLatency(\n          sendMessage,\n          [...hmrUpdate.updatedModules],\n          hmrUpdate.startMsSinceEpoch,\n          hmrUpdate.endMsSinceEpoch,\n          // suppress the `client-hmr-latency` event if the update was a no-op:\n          hmrUpdate.hasUpdates\n        )\n      }\n      dispatcher.onBuildOk()\n    } else {\n      tryApplyUpdatesWebpack(sendMessage, dispatcher)\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        setPendingHotUpdateWebpack()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `${NEXT_HMR_REFRESH_HASH_COOKIE}=${obj.hash}`\n\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      turbopackHmr?.onReloadPage()\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      turbopackHmr?.onPageAddRemove()\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    default: {\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useErrorOverlayReducer('app')\n\n  const dispatcher = useMemo<Dispatcher>(() => {\n    return {\n      onBuildOk() {\n        dispatch({ type: ACTION_BUILD_OK })\n      },\n      onBuildError(message) {\n        dispatch({ type: ACTION_BUILD_ERROR, message })\n      },\n      onBeforeRefresh() {\n        dispatch({ type: ACTION_BEFORE_REFRESH })\n      },\n      onRefresh() {\n        dispatch({ type: ACTION_REFRESH })\n      },\n      onVersionInfo(versionInfo) {\n        dispatch({ type: ACTION_VERSION_INFO, versionInfo })\n      },\n      onStaticIndicator(status: boolean) {\n        dispatch({ type: ACTION_STATIC_INDICATOR, staticIndicator: status })\n      },\n      onDebugInfo(debugInfo) {\n        dispatch({ type: ACTION_DEBUG_INFO, debugInfo })\n      },\n      onDevIndicator(devIndicator) {\n        dispatch({\n          type: ACTION_DEV_INDICATOR,\n          devIndicator,\n        })\n      },\n    }\n  }, [dispatch])\n\n  const handleOnUnhandledError = useCallback(\n    (error: Error): void => {\n      // Component stack is added to the error in use-error-handler in case there was a hydration error\n      const componentStackTrace = (error as any)._componentStack\n\n      dispatch({\n        type: ACTION_UNHANDLED_ERROR,\n        reason: error,\n        frames: parseStack(error.stack || ''),\n        componentStackFrames:\n          typeof componentStackTrace === 'string'\n            ? parseComponentStack(componentStackTrace)\n            : undefined,\n      })\n    },\n    [dispatch]\n  )\n\n  const handleOnUnhandledRejection = useCallback(\n    (reason: Error): void => {\n      const stitchedError = getReactStitchedError(reason)\n      dispatch({\n        type: ACTION_UNHANDLED_REJECTION,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n      })\n    },\n    [dispatch]\n  )\n  useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname, dispatcher])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        handleDevBuildIndicatorHmrEvents(obj)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          dispatcher,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: unknown) {\n        reportInvalidHmrMessage(event, err)\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    dispatcher,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n\n  return (\n    <AppDevOverlay state={state} globalError={globalError}>\n      {children}\n    </AppDevOverlay>\n  )\n}\n"], "names": ["useCallback", "useEffect", "startTransition", "useMemo", "useRef", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "REACT_REFRESH_FULL_RELOAD", "reportInvalidHmrMessage", "useErrorOverlayReducer", "parseStack", "AppDevOverlay", "useErrorHandler", "RuntimeError<PERSON>andler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "useUntrackedPathname", "getReactStitchedError", "handleDevBuildIndicatorHmrEvents", "reportHmrLatency", "TurbopackHmr", "NEXT_HMR_REFRESH_HASH_COOKIE", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "webpackStartMsSinceEpoch", "turbopackHmr", "process", "env", "TURBOPACK", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "waitForWebpackRuntimeHotUpdate", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "dependency<PERSON><PERSON>n", "undefined", "window", "location", "reload", "tryApplyUpdatesWebpack", "dispatcher", "onBuildOk", "handleApplyUpdates", "updatedModules", "console", "warn", "onRefresh", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "onBeforeRefresh", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "length", "error", "handleHotUpdate", "hmrUpdate", "onBuilt", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "action", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "onBuilding", "log", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "onTurbopackMessage", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "document", "cookie", "hmrRefresh", "RELOAD_PAGE", "onReloadPage", "ADDED_PAGE", "REMOVED_PAGE", "onPageAddRemove", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "globalError", "state", "dispatch", "staticIndicator", "debugInfo", "handleOnUnhandledError", "componentStackTrace", "_componentStack", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "stitchedError", "webSocketRef", "pathname", "appIsrManifest", "DOMException", "websocket", "addEventListener", "removeEventListener"], "mappings": "AAAA,6CAA6C;;AAG7C,SAASA,WAAW,EAAEC,SAAS,EAAEC,eAAe,EAAEC,OAAO,EAAEC,MAAM,QAAQ,QAAO;AAChF,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,mCAAkC;AACpE,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,oBAAoB,EACpBC,cAAc,EACdC,uBAAuB,EACvBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,yBAAyB,EACzBC,uBAAuB,EACvBC,sBAAsB,QACjB,YAAW;AAClB,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,iCAAgC;AAChE,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,yBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,iCAAgC;AAEpE,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,oCAAoC,QAAQ,YAAW;AAEhE,SAASC,oBAAoB,QAAQ,6BAA4B;AACjE,SAASC,qBAAqB,QAAQ,8BAA6B;AACnE,SAASC,gCAAgC,QAAQ,kFAAiF;AAGlI,OAAOC,sBAAsB,8BAA6B;AAC1D,SAASC,YAAY,QAAQ,yCAAwC;AACrE,SAASC,4BAA4B,QAAQ,2BAA0B;AAavE,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,2BAA0C;AAC9C,MAAMC,eAAoCC,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAId,iBACJ;AAEJ,IAAIe,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEA,OAAO,SAASG;IACd,OAAOL;AACT;AAEA,kDAAkD;AAClD,SAASM,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpB,4BAA4BoB;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIX,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOZ,8BAA8BsB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACAJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAAC1D,oBAAoB0D,eAAe;QACtDC,iBAAiBZ,MAAMA,IAAIY,eAAe,GAAGC;IAC/C;IAGF,IAAIvC,WAAW;IACfA,YAAY;IACZwC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,uBACPhB,WAAsC,EACtCiB,UAAsB;IAEtB,IAAI,CAAC9B,uBAAuB,CAACE,mBAAmB;QAC9CP;QACAmC,WAAWC,SAAS;QACpBvD,iBAAiBqC,aAAa,EAAE,EAAE1B,0BAA2BH,KAAKC,GAAG;QACrE;IACF;IAEA,SAAS+C,mBACPpB,GAAQ,EACRqB,cAA0C;QAE1C,IAAIrB,OAAO/C,oBAAoB0D,eAAe,IAAIU,kBAAkB,MAAM;YACxE,IAAIrB,KAAK;gBACPsB,QAAQC,IAAI,CAAC5E;YACf,OAAO,IAAIM,oBAAoB0D,eAAe,EAAE;gBAC9CW,QAAQC,IAAI,CAAC/D;YACf;YACAuC,kBAAkBC,KAAKC;YACvB;QACF;QAEAiB,WAAWC,SAAS;QAEpB,IAAI/B,qBAAqB;YACvB,+DAA+D;YAC/D6B,uBAAuBhB,aAAaiB;YACpC;QACF;QAEAA,WAAWM,SAAS;QACpBzC;QACAnB,iBACEqC,aACAoB,gBACA9C,0BACAH,KAAKC,GAAG;QAGV,IAAII,QAAQC,GAAG,CAAC+C,gBAAgB,EAAE;YAChC/B,kBAAkB;gBAChB,IAAIgC,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3DpC,OAAOC,GAAG,CACPoC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACR;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGH,WAAWY,eAAe;QAC1B,2DAA2D;QAC3D,OAAOvC,OAAOC,GAAG,CAACuC,KAAK;IACzB,GACCF,IAAI,CACH,CAACR;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACrB;QACCoB,mBAAmBpB,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASgC,eACPC,GAAqB,EACrBhC,WAAsC,EACtCiC,uBAA6D,EAC7DC,MAAoC,EACpCjB,UAAsB,EACtBkB,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYzG,sBAAsB;YACtCwG,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7BvB,WAAWwB,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACK,MAAM,EAAED,IAAK;YAChDrB,QAAQuB,KAAK,CAAC/G,UAAU0G,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIlE,QAAQC,GAAG,CAAC+C,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACa,UAAUD,MAAM,CAAC,EAAE;gBACtCb,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASmB;QACP,IAAIrE,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB,MAAMoE,YAAYvE,aAAcwE,OAAO;YACvC,IAAID,aAAa,MAAM;gBACrBnF,iBACEqC,aACA;uBAAI8C,UAAU1B,cAAc;iBAAC,EAC7B0B,UAAUE,iBAAiB,EAC3BF,UAAUG,eAAe,EACzB,qEAAqE;gBACrEH,UAAUI,UAAU;YAExB;YACAjC,WAAWC,SAAS;QACtB,OAAO;YACLF,uBAAuBhB,aAAaiB;QACtC;IACF;IAEA,OAAQe,IAAImB,MAAM;QAChB,KAAK7F,4BAA4B8F,YAAY;YAAE;gBAC7C,IAAI5E,QAAQC,GAAG,CAAC4E,oBAAoB,EAAE;oBACpC,IAAIlB,mBAAmB;wBACrBA,kBAAkBmB,OAAO,GAAGtB,IAAIuB,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAACnB,YAAYkB,OAAO,IAAetB,IAAIuB,IAAI,EAAE;4BAC/CtC,WAAWuC,iBAAiB,CAAC;wBAC/B,OAAO;4BACLvC,WAAWuC,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAKlG,4BAA4BmG,QAAQ;YAAE;gBACzC,IAAIjF,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBH,aAAcmF,UAAU;gBAC1B,OAAO;oBACLpF,2BAA2BH,KAAKC,GAAG;oBACnCW;oBACAsC,QAAQsC,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKrG,4BAA4BsG,KAAK;QACtC,KAAKtG,4BAA4BuG,IAAI;YAAE;gBACrC,IAAI7B,IAAI9C,IAAI,EAAE;oBACZD,oBAAoB+C,IAAI9C,IAAI;gBAC9B;gBAEA,MAAM,EAAEoD,MAAM,EAAEE,QAAQ,EAAE,GAAGR;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKf,WAAW6C,aAAa,CAAC9B,IAAI+B,WAAW;gBAClE,IAAI,WAAW/B,OAAOA,IAAIgC,KAAK,EAAE/C,WAAWgD,WAAW,CAACjC,IAAIgC,KAAK;gBACjE,IAAI,kBAAkBhC,KAAKf,WAAWiD,cAAc,CAAClC,IAAImC,YAAY;gBAErE,MAAMC,YAAYC,QAAQ/B,UAAUA,OAAOK,MAAM;gBACjD,kEAAkE;gBAClE,IAAIyB,WAAW;oBACbpE,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP6D,YAAYhC,OAAOK,MAAM;wBACzB4B,UAAUxG;oBACZ;oBAGFsE,aAAaC;oBACb;gBACF;gBAEA,MAAMkC,cAAcH,QAAQ7B,YAAYA,SAASG,MAAM;gBACvD,IAAI6B,aAAa;oBACfxE,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPgE,cAAcjC,SAASG,MAAM;wBAC7B4B,UAAUxG;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM2G,oBAAoB5I,sBAAsB;wBAC9C0G,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAIgC,kBAAkBlC,QAAQ,CAACG,MAAM,EAAED,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXrB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAACzF,UAAU6I,kBAAkBlC,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA1C,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8D,UAAUxG;gBACZ;gBAGF,IAAIiE,IAAImB,MAAM,KAAK7F,4BAA4BsG,KAAK,EAAE;oBACpDf;gBACF;gBACA;YACF;QACA,KAAKvF,4BAA4BqH,mBAAmB;YAAE;gBACpD1C,wBAAwB;oBACtB2C,MAAMtH,4BAA4BqH,mBAAmB;oBACrDpB,MAAM;wBACJsB,WAAW7C,IAAIuB,IAAI,CAACsB,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAKvH,4BAA4BwH,iBAAiB;YAAE;gBAClDvG,aAAcwG,kBAAkB,CAAC/C;gBACjCf,WAAWY,eAAe;gBAC1BI,wBAAwB;oBACtB2C,MAAMtH,4BAA4BwH,iBAAiB;oBACnDvB,MAAMvB,IAAIuB,IAAI;gBAChB;gBACA,IAAIvG,oBAAoB0D,eAAe,EAAE;oBACvCW,QAAQC,IAAI,CAAC/D;oBACbuC,kBAAkB,MAAME;gBAC1B;gBACAiB,WAAWM,SAAS;gBACpB;YACF;QACA,uDAAuD;QACvD,KAAKjE,4BAA4B0H,wBAAwB;YAAE;gBACzDzG,gCAAAA,aAAc0G,wBAAwB;gBACtCjF,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8D,UAAUxG;oBACVmB,MAAM8C,IAAI9C,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvCgG,SAASC,MAAM,GAAG,AAAGtH,+BAA6B,MAAGmE,IAAI9C,IAAI;gBAE7D,IAAIlC,oBAAoB0D,eAAe,EAAE;oBACvC,IAAIrC,WAAW;oBACfA,YAAY;oBACZ,OAAOwC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBAEArF,gBAAgB;oBACdwG,OAAOkD,UAAU;oBACjBnE,WAAWM,SAAS;gBACtB;gBAEA,IAAI/C,QAAQC,GAAG,CAAC+C,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKpE,4BAA4B+H,WAAW;YAAE;gBAC5C9G,gCAAAA,aAAc+G,YAAY;gBAC1BtF,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP8D,UAAUxG;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOwC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAKzD,4BAA4BiI,UAAU;QAC3C,KAAKjI,4BAA4BkI,YAAY;YAAE;gBAC7CjH,gCAAAA,aAAckH,eAAe;gBAC7B,qFAAqF;gBACrF,OAAOvD,OAAOkD,UAAU;YAC1B;QACA,KAAK9H,4BAA4BoI,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAG3D;gBACtB,IAAI2D,WAAW;oBACb,MAAM,EAAErF,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAKqF,KAAK,CAACD;oBACtC,MAAM/C,QAAQ,qBAAkB,CAAlB,IAAIiD,MAAMvF,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BsC,MAAM1C,KAAK,GAAGA;oBACdmC,aAAa;wBAACO;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKtF,4BAA4BwI,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEA,eAAe,SAASC,UAAU,KAQjC;IARiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShC,MAAM,CAACC,OAAOC,SAAS,GAAGxJ,uBAAuB;IAEjD,MAAMqE,aAAatF,QAAoB;QACrC,OAAO;YACLuF;gBACEkF,SAAS;oBAAExB,MAAM1I;gBAAgB;YACnC;YACAuG,cAAanC,OAAO;gBAClB8F,SAAS;oBAAExB,MAAM3I;oBAAoBqE;gBAAQ;YAC/C;YACAuB;gBACEuE,SAAS;oBAAExB,MAAM5I;gBAAsB;YACzC;YACAuF;gBACE6E,SAAS;oBAAExB,MAAMvI;gBAAe;YAClC;YACAyH,eAAcC,WAAW;gBACvBqC,SAAS;oBAAExB,MAAMnI;oBAAqBsH;gBAAY;YACpD;YACAP,mBAAkBhE,MAAe;gBAC/B4G,SAAS;oBAAExB,MAAMtI;oBAAyB+J,iBAAiB7G;gBAAO;YACpE;YACAyE,aAAYqC,SAAS;gBACnBF,SAAS;oBAAExB,MAAMzI;oBAAmBmK;gBAAU;YAChD;YACApC,gBAAeC,YAAY;gBACzBiC,SAAS;oBACPxB,MAAMxI;oBACN+H;gBACF;YACF;QACF;IACF,GAAG;QAACiC;KAAS;IAEb,MAAMG,yBAAyB/K,YAC7B,CAACoH;QACC,iGAAiG;QACjG,MAAM4D,sBAAsB,AAAC5D,MAAc6D,eAAe;QAE1DL,SAAS;YACPxB,MAAMrI;YACNmK,QAAQ9D;YACR+D,QAAQ9J,WAAW+F,MAAM1C,KAAK,IAAI;YAClC0G,sBACE,OAAOJ,wBAAwB,WAC3BnJ,oBAAoBmJ,uBACpB5F;QACR;IACF,GACA;QAACwF;KAAS;IAGZ,MAAMS,6BAA6BrL,YACjC,CAACkL;QACC,MAAMI,gBAAgBrJ,sBAAsBiJ;QAC5CN,SAAS;YACPxB,MAAMpI;YACNkK,QAAQI;YACRH,QAAQ9J,WAAWiK,cAAc5G,KAAK,IAAI;QAC5C;IACF,GACA;QAACkG;KAAS;IAEZrJ,gBAAgBwJ,wBAAwBM;IAExC,MAAME,eAAe5J,aAAa6I;IAClC5I,iBAAiB2J;IACjB,MAAM/G,cAAc/C,eAAe8J;IACnC,MAAM9E,0BAA0B/E,aAAa8C,aAAa,CAACD,MACzDD,kBAAkBC,KAAKC;IAGzB,MAAMkC,SAASnG;IAEf,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMiL,WAAWxJ;IACjB,MAAM2E,oBAAoBvG,OAAuC,CAAC;IAClE,MAAMwG,cAAcxG,OAAOoL;IAE3B,IAAIxI,QAAQC,GAAG,CAAC4E,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtD5H,UAAU;YACR2G,YAAYkB,OAAO,GAAG0D;YAEtB,MAAMC,iBAAiB9E,kBAAkBmB,OAAO;YAEhD,IAAI2D,gBAAgB;gBAClB,IAAID,YAAYA,YAAYC,gBAAgB;oBAC1C,IAAI;wBACFhG,WAAWuC,iBAAiB,CAAC;oBAC/B,EAAE,OAAOkD,QAAQ;wBACf,IAAIpG,UAAU;wBAEd,IAAIoG,kBAAkBQ,cAAc;gCAExBR;4BADV,sEAAsE;4BACtEpG,UAAUoG,CAAAA,gBAAAA,OAAOxG,KAAK,YAAZwG,gBAAgBA,OAAOpG,OAAO;wBAC1C,OAAO,IAAIoG,kBAAkBb,OAAO;gCACaa;4BAA/CpG,UAAU,YAAYoG,OAAOpG,OAAO,GAAG,OAAQoG,CAAAA,CAAAA,iBAAAA,OAAOxG,KAAK,YAAZwG,iBAAgB,EAAC;wBAClE,OAAO;4BACLpG,UAAU,2BAA2BoG;wBACvC;wBAEArF,QAAQC,IAAI,CAAC,WAAWhB;oBAC1B;gBACF,OAAO;oBACLW,WAAWuC,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAACwD;YAAU/F;SAAW;IAC3B;IAEAxF,UAAU;QACR,MAAM0L,YAAYJ,aAAazD,OAAO;QACtC,IAAI,CAAC6D,WAAW;QAEhB,MAAMxH,UAAU,CAACc;YACf,IAAI;gBACF,MAAMuB,MAAMzB,KAAKqF,KAAK,CAACnF,MAAM8C,IAAI;gBACjC7F,iCAAiCsE;gBACjCD,eACEC,KACAhC,aACAiC,yBACAC,QACAjB,YACAkB,mBACAC;YAEJ,EAAE,OAAOrC,KAAc;gBACrBpD,wBAAwB8D,OAAOV;YACjC;QACF;QAEAoH,UAAUC,gBAAgB,CAAC,WAAWzH;QACtC,OAAO,IAAMwH,UAAUE,mBAAmB,CAAC,WAAW1H;IACxD,GAAG;QACDK;QACAkC;QACA6E;QACA9F;QACAgB;QACAE;KACD;IAED,qBACE,KAACrF;QAAcqJ,OAAOA;QAAOD,aAAaA;kBACvCD;;AAGP"}