{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/websocket.ts"], "sourcesContent": ["import {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type HMR_ACTION_TYPES,\n} from '../../../../server/dev/hot-reloader-types'\nimport { getSocketUrl } from '../utils/get-socket-url'\n\nlet source: WebSocket\n\ntype ActionCallback = (action: HMR_ACTION_TYPES) => void\n\nconst eventCallbacks: Array<ActionCallback> = []\n\nexport function addMessageListener(callback: ActionCallback) {\n  eventCallbacks.push(callback)\n}\n\nexport function sendMessage(data: string) {\n  if (!source || source.readyState !== source.OPEN) return\n  return source.send(data)\n}\n\nlet reconnections = 0\nlet reloading = false\nlet serverSessionId: number | null = null\n\nexport function connectHMR(options: { path: string; assetPrefix: string }) {\n  function init() {\n    if (source) source.close()\n\n    function handleOnline() {\n      reconnections = 0\n      window.console.log('[HMR] connected')\n    }\n\n    function handleMessage(event: MessageEvent<string>) {\n      // While the page is reloading, don't respond to any more messages.\n      // On reconnect, the server may send an empty list of changes if it was restarted.\n      if (reloading) {\n        return\n      }\n\n      // Coerce into HMR_ACTION_TYPES as that is the format.\n      const msg: HMR_ACTION_TYPES = JSON.parse(event.data)\n\n      if (\n        'action' in msg &&\n        msg.action === HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      ) {\n        if (\n          serverSessionId !== null &&\n          serverSessionId !== msg.data.sessionId\n        ) {\n          // Either the server's session id has changed and it's a new server, or\n          // it's been too long since we disconnected and we should reload the page.\n          // There could be 1) unhandled server errors and/or 2) stale content.\n          // Perform a hard reload of the page.\n          window.location.reload()\n\n          reloading = true\n          return\n        }\n\n        serverSessionId = msg.data.sessionId\n      }\n\n      for (const eventCallback of eventCallbacks) {\n        eventCallback(msg)\n      }\n    }\n\n    let timer: ReturnType<typeof setTimeout>\n    function handleDisconnect() {\n      source.onerror = null\n      source.onclose = null\n      source.close()\n      reconnections++\n      // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n      if (reconnections > 25) {\n        reloading = true\n        window.location.reload()\n        return\n      }\n\n      clearTimeout(timer)\n      // Try again after 5 seconds\n      timer = setTimeout(init, reconnections > 5 ? 5000 : 1000)\n    }\n\n    const url = getSocketUrl(options.assetPrefix)\n\n    source = new window.WebSocket(`${url}${options.path}`)\n    source.onopen = handleOnline\n    source.onerror = handleDisconnect\n    source.onclose = handleDisconnect\n    source.onmessage = handleMessage\n  }\n\n  init()\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER", "getSocketUrl", "source", "eventCallbacks", "addMessageListener", "callback", "push", "sendMessage", "data", "readyState", "OPEN", "send", "reconnections", "reloading", "serverSessionId", "connectHMR", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "action", "TURBOPACK_CONNECTED", "sessionId", "location", "reload", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "clearTimeout", "setTimeout", "url", "assetPrefix", "WebSocket", "path", "onopen", "onmessage"], "mappings": "AAAA,SACEA,2BAA2B,QAEtB,4CAA2C;AAClD,SAASC,YAAY,QAAQ,0BAAyB;AAEtD,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,OAAO,SAASC,mBAAmBC,QAAwB;IACzDF,eAAeG,IAAI,CAACD;AACtB;AAEA,OAAO,SAASE,YAAYC,IAAY;IACtC,IAAI,CAACN,UAAUA,OAAOO,UAAU,KAAKP,OAAOQ,IAAI,EAAE;IAClD,OAAOR,OAAOS,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AACpB,IAAIC,YAAY;AAChB,IAAIC,kBAAiC;AAErC,OAAO,SAASC,WAAWC,OAA8C;IACvE,SAASC;QACP,IAAIf,QAAQA,OAAOgB,KAAK;QAExB,SAASC;YACPP,gBAAgB;YAChBQ,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,mEAAmE;YACnE,kFAAkF;YAClF,IAAIX,WAAW;gBACb;YACF;YAEA,sDAAsD;YACtD,MAAMY,MAAwBC,KAAKC,KAAK,CAACH,MAAMhB,IAAI;YAEnD,IACE,YAAYiB,OACZA,IAAIG,MAAM,KAAK5B,4BAA4B6B,mBAAmB,EAC9D;gBACA,IACEf,oBAAoB,QACpBA,oBAAoBW,IAAIjB,IAAI,CAACsB,SAAS,EACtC;oBACA,uEAAuE;oBACvE,0EAA0E;oBAC1E,qEAAqE;oBACrE,qCAAqC;oBACrCV,OAAOW,QAAQ,CAACC,MAAM;oBAEtBnB,YAAY;oBACZ;gBACF;gBAEAC,kBAAkBW,IAAIjB,IAAI,CAACsB,SAAS;YACtC;YAEA,KAAK,MAAMG,iBAAiB9B,eAAgB;gBAC1C8B,cAAcR;YAChB;QACF;QAEA,IAAIS;QACJ,SAASC;YACPjC,OAAOkC,OAAO,GAAG;YACjBlC,OAAOmC,OAAO,GAAG;YACjBnC,OAAOgB,KAAK;YACZN;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBC,YAAY;gBACZO,OAAOW,QAAQ,CAACC,MAAM;gBACtB;YACF;YAEAM,aAAaJ;YACb,4BAA4B;YAC5BA,QAAQK,WAAWtB,MAAML,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAM4B,MAAMvC,aAAae,QAAQyB,WAAW;QAE5CvC,SAAS,IAAIkB,OAAOsB,SAAS,CAAC,AAAC,KAAEF,MAAMxB,QAAQ2B,IAAI;QACnDzC,OAAO0C,MAAM,GAAGzB;QAChBjB,OAAOkC,OAAO,GAAGD;QACjBjC,OAAOmC,OAAO,GAAGF;QACjBjC,OAAO2C,SAAS,GAAGtB;IACrB;IAEAN;AACF"}