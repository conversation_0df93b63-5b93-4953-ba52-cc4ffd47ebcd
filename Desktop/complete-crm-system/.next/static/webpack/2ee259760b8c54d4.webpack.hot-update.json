{"c": ["app/layout", "webpack"], "r": ["app/dashboard/page"], "m": ["(app-pages-browser)/./app/dashboard/page.tsx", "(app-pages-browser)/./components/dashboard/LeadFormDemo.tsx", "(app-pages-browser)/./components/dashboard/forms/lead-capture-form.tsx", "(app-pages-browser)/./node_modules/@hookform/resolvers/dist/resolvers.mjs", "(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/property-expr/index.js", "(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs", "(app-pages-browser)/./node_modules/tiny-case/index.js", "(app-pages-browser)/./node_modules/toposort/index.js", "(app-pages-browser)/./node_modules/yup/index.esm.js"]}