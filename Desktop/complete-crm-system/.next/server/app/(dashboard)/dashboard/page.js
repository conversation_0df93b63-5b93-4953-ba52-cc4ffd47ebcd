/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/dashboard/page";
exports.ids = ["app/(dashboard)/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"24da772ecc54\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9pbm5vdmFyc19sYWIvRGVza3RvcC9jb21wbGV0ZS1jcm0tc3lzdGVtL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI0ZGE3NzJlY2M1NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst metadata = {\n    title: 'Complete CRM System',\n    description: 'Een complete CRM oplossing voor lead management'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"nl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzQjtBQUdoQkE7QUFFQyxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUM3Qks7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9pbm5vdmFyc19sYWIvRGVza3RvcC9jb21wbGV0ZS1jcm0tc3lzdGVtL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ29tcGxldGUgQ1JNIFN5c3RlbScsXG4gIGRlc2NyaXB0aW9uOiAnRWVuIGNvbXBsZXRlIENSTSBvcGxvc3Npbmcgdm9vciBsZWFkIG1hbmFnZW1lbnQnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwibmxcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(rsc)/./app/(dashboard)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(rsc)/./app/(dashboard)/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\"],\n'not-found': [module5, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/complete-crm-system/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(rsc)/./app/(dashboard)/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRGVza3RvcCUyRmNvbXBsZXRlLWNybS1zeXN0ZW0lMkZhcHAlMkYoZGFzaGJvYXJkKSUyRmRhc2hib2FyZCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9pbm5vdmFyc19sYWIvRGVza3RvcC9jb21wbGV0ZS1jcm0tc3lzdGVtL2FwcC8oZGFzaGJvYXJkKS9kYXNoYm9hcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(rsc)/./app/(dashboard)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRGVza3RvcCUyRmNvbXBsZXRlLWNybS1zeXN0ZW0lMkZhcHAlMkYoZGFzaGJvYXJkKSUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUErRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2lubm92YXJzX2xhYi9EZXNrdG9wL2NvbXBsZXRlLWNybS1zeXN0ZW0vYXBwLyhkYXNoYm9hcmQpL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_LeadFormDemo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/LeadFormDemo */ \"(ssr)/./components/dashboard/LeadFormDemo.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst DashboardPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Welkom bij je CRM dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Snelle Statistieken\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Nieuwe Leads\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Conversies\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_LeadFormDemo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/dashboard/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/layout.tsx":
/*!************************************!*\
  !*** ./app/(dashboard)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CogIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,MagnifyingGlassIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Navigation Items\nconst navigationItems = [\n    {\n        name: 'Dashboard',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        current: false\n    },\n    {\n        name: 'Leads',\n        href: '/dashboard/leads',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false,\n        badge: 'New'\n    },\n    {\n        name: 'Analytics',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'Email Campaigns',\n        href: '/dashboard/campaigns',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    },\n    {\n        name: 'SEO Tools',\n        href: '/dashboard/seo',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        current: false\n    },\n    {\n        name: 'Chat Support',\n        href: '/dashboard/chat',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        current: false\n    },\n    {\n        name: 'Reports',\n        href: '/dashboard/reports',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        current: false\n    },\n    {\n        name: 'Settings',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        current: false\n    }\n];\nfunction DashboardLayout({ children }) {\n    // State\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(3);\n    // Hooks\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Mock user data (replace with real auth later)\n    const mockUser = {\n        name: 'Demo User',\n        email: '<EMAIL>',\n        company: 'Demo Company',\n        avatar: null\n    };\n    // Logout Handler (placeholder)\n    const handleLogout = async ()=>{\n        alert('Logout functionaliteit komt later');\n    };\n    // Update Navigation Current State\n    const updatedNavigation = navigationItems.map((item)=>({\n            ...item,\n            current: pathname === item.href || pathname.startsWith(item.href + '/')\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 z-40 lg:hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                            onClick: ()=>setSidebarOpen(false)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                x: -300\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -300\n                            },\n                            className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-16 shrink-0 items-center justify-between px-6 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"CRM Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex-1 px-4 py-6 space-y-2\",\n                                    children: updatedNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${item.current ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n                                            onClick: ()=>setSidebarOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-3 h-5 w-5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.name,\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 flex-shrink-0 items-center px-6 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"CRM Platform\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-2\",\n                            children: updatedNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${item.current ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name,\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 border-t border-gray-200 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group block w-full rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: mockUser.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"h-10 w-10 rounded-full\",\n                                                src: mockUser.avatar,\n                                                alt: mockUser.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold\",\n                                                children: mockUser.name?.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: mockUser.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: mockUser.company || mockUser.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"ml-3 text-gray-400 hover:text-gray-600 transition-colors\",\n                                            title: \"Uitloggen\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden text-gray-500 hover:text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-md mx-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Zoek leads, campaigns...\",\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"relative text-gray-500 hover:text-gray-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CogIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_MagnifyingGlassIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                notifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                    children: notifications\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:hidden\",\n                                            children: mockUser.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"h-8 w-8 rounded-full\",\n                                                src: mockUser.avatar,\n                                                alt: mockUser.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-semibold\",\n                                                children: mockUser.name?.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/complete-crm-system/app/(dashboard)/layout.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"24da772ecc54\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9pbm5vdmFyc19sYWIvRGVza3RvcC9jb21wbGV0ZS1jcm0tc3lzdGVtL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI0ZGE3NzJlY2M1NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/LeadFormDemo.tsx":
/*!***********************************************!*\
  !*** ./components/dashboard/LeadFormDemo.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_forms_lead_capture_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/forms/lead-capture-form */ \"(ssr)/./components/dashboard/forms/lead-capture-form.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst LeadFormDemo = ()=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleSubmit = async (data)=>{\n        setIsLoading(true);\n        try {\n            // Simuleer een API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            console.log('Lead data:', data);\n            alert('Lead succesvol verzonden!');\n        } catch (error) {\n            console.error('Error:', error);\n            alert('Er is een fout opgetreden bij het verzenden.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-lg mx-auto p-6 bg-white rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                children: \"Lead Capture Demo\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/LeadFormDemo.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_forms_lead_capture_form__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                onSubmit: handleSubmit,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/LeadFormDemo.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/LeadFormDemo.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LeadFormDemo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/LeadFormDemo.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/forms/lead-capture-form.tsx":
/*!**********************************************************!*\
  !*** ./components/dashboard/forms/lead-capture-form.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeadCaptureForm: () => (/* binding */ LeadCaptureForm),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(ssr)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(ssr)/./node_modules/yup/index.esm.js\");\n\n\n\n\n\nconst schema = yup__WEBPACK_IMPORTED_MODULE_3__.object({\n    name: yup__WEBPACK_IMPORTED_MODULE_3__.string().required('Naam is verplicht').min(2, 'Naam moet minimaal 2 tekens bevatten'),\n    email: yup__WEBPACK_IMPORTED_MODULE_3__.string().required('E-mail is verplicht').email('Vul een geldig e-mailadres in'),\n    company: yup__WEBPACK_IMPORTED_MODULE_3__.string().required('Bedrijfsnaam is verplicht').min(2, 'Bedrijfsnaam moet minimaal 2 tekens bevatten'),\n    phone: yup__WEBPACK_IMPORTED_MODULE_3__.string().matches(/^[0-9+\\-\\s()]*$/, 'Vul een geldig telefoonnummer in'),\n    message: yup__WEBPACK_IMPORTED_MODULE_3__.string().required('Bericht is verplicht').min(10, 'Bericht moet minimaal 10 tekens bevatten')\n}).required();\nconst LeadCaptureForm = ({ onSubmit, isLoading = false })=>{\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_1__.yupResolver)(schema)\n    });\n    const onSubmitForm = (data)=>{\n        onSubmit(data);\n        reset();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(onSubmitForm),\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"name\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Naam\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        id: \"name\",\n                        ...register('name'),\n                        className: `mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\n            ${errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.name.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"email\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"E-mail\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"email\",\n                        id: \"email\",\n                        ...register('email'),\n                        className: `mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\n            ${errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.email.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"company\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Bedrijf\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        id: \"company\",\n                        ...register('company'),\n                        className: `mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\n            ${errors.company ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    errors.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.company.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"phone\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Telefoon (optioneel)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"tel\",\n                        id: \"phone\",\n                        ...register('phone'),\n                        className: `mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\n            ${errors.phone ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.phone.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"message\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Bericht\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        id: \"message\",\n                        rows: 4,\n                        ...register('message'),\n                        className: `mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\n            ${errors.message ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.message.message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"submit\",\n                disabled: isLoading,\n                className: `w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white\n          ${isLoading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'}`,\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined),\n                        \"Verzenden...\"\n                    ]\n                }, void 0, true) : 'Verstuur'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/complete-crm-system/components/dashboard/forms/lead-capture-form.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LeadCaptureForm);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/forms/lead-capture-form.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/dashboard/page.tsx */ \"(ssr)/./app/(dashboard)/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRGVza3RvcCUyRmNvbXBsZXRlLWNybS1zeXN0ZW0lMkZhcHAlMkYoZGFzaGJvYXJkKSUyRmRhc2hib2FyZCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9pbm5vdmFyc19sYWIvRGVza3RvcC9jb21wbGV0ZS1jcm0tc3lzdGVtL2FwcC8oZGFzaGJvYXJkKS9kYXNoYm9hcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/layout.tsx */ \"(ssr)/./app/(dashboard)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRGVza3RvcCUyRmNvbXBsZXRlLWNybS1zeXN0ZW0lMkZhcHAlMkYoZGFzaGJvYXJkKSUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUErRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2lubm92YXJzX2xhYi9EZXNrdG9wL2NvbXBsZXRlLWNybS1zeXN0ZW0vYXBwLyhkYXNoYm9hcmQpL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2F(dashboard)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@heroicons","vendor-chunks/@hookform","vendor-chunks/react-hook-form","vendor-chunks/yup","vendor-chunks/toposort","vendor-chunks/tiny-case","vendor-chunks/property-expr"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDesktop%2Fcomplete-crm-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();